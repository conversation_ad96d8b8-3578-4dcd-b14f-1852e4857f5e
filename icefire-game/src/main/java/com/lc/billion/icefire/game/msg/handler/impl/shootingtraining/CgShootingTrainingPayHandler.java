package com.lc.billion.icefire.game.msg.handler.impl.shootingtraining;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.shootingtraining.ShootingTrainingServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgShootingTrainingPay;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * 射击训练射击, 根据选中的type掉落
 * <AUTHOR>
 * @Date: 2021/9/28 10:37
 */
@Controller
public class CgShootingTrainingPayHandler extends CgAbstractMessageHandler<CgShootingTrainingPay> {
	@Autowired ShootingTrainingServiceImpl shootingTrainingService;

	@Override protected void handle(Role role, CgShootingTrainingPay message) {
		shootingTrainingService.shootingTrainingPay(role);
	}
}

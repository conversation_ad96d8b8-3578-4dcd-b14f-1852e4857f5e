/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package com.lc.billion.icefire.game.net;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.longtech.cod.socket.netty.NettyTraffic;
import com.simfun.sgf.net.MsgPack;
import com.simfun.sgf.net.NetConfig;
import com.simfun.sgf.net.NetMessageDecoder;
import com.simfun.sgf.net.NetMessageEncoder;
import com.simfun.sgf.net.codec.thrift.ThriftBodyDecoder;
import com.simfun.sgf.net.codec.thrift.ThriftBodyEncoder;
import com.simfun.sgf.net.compress.lz4.Lz4BodyCompressor;
import com.simfun.sgf.net.compress.lz4.Lz4BodyDecompressor;
import com.simfun.sgf.net.msg.MessageConfig;
import com.simfun.sgf.net.msg.MessageConfigManager;
import io.netty.channel.*;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.cors.CorsConfigBuilder;
import io.netty.handler.codec.http.cors.CorsHandler;
import io.netty.handler.codec.http.websocketx.WebSocketFrameAggregator;
import io.netty.handler.codec.http.websocketx.extensions.compression.WebSocketServerCompressionHandler;
import io.netty.handler.ssl.SslContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.SocketAddress;
import java.nio.channels.ClosedChannelException;

/**
 */
public class WebSocketServerInitializer extends ChannelInitializer<SocketChannel> {
    private static final Logger log = LoggerFactory.getLogger(WebSocketServerInitializer.class);
    private static final String WEBSOCKET_PATH = "/websocket";

    private final SslContext sslCtx;
    private NetMessageEncoder messageEncoder;

    public WebSocketServerInitializer(SslContext sslCtx) {
        this.sslCtx = sslCtx;
    }

    public NetMessageEncoder getMessageEncoder() {
        if(messageEncoder == null){
            NetConfig gcNetConfig = new NetConfig();
            gcNetConfig.setEnableSequenceMode(true);
            @SuppressWarnings("unchecked")
            MessageConfigManager<MessageConfig.MessageMeta> messageConfigMgr = Application.getBean(MessageConfigManager.class);
            messageEncoder = new NetMessageEncoder(gcNetConfig, messageConfigMgr, new ThriftBodyEncoder(), new Lz4BodyCompressor());
        }
        return messageEncoder;
    }

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();
        pipeline.addLast(new HttpServerCodec());
        pipeline.addLast(new CorsHandler(CorsConfigBuilder.forAnyOrigin().build()));
        pipeline.addLast(new HttpObjectAggregator(6553600));
        pipeline.addLast(new WebSocketServerCompressionHandler());
        pipeline.addLast(new WebSocketFrameAggregator(6553600));
        pipeline.addLast(new WebSocketServerHandler());
        NetConfig gcNetConfig = new NetConfig();
        gcNetConfig.setEnableSequenceMode(true);

        log.info("initChannel:{}, GlobalChannelTrafficShapingHandler: {}, GlobalTrafficShapingHandler: {}", ch,
                NettyTraffic.getGlobalChannelTrafficShapingHandler(), NettyTraffic.getGlobalTrafficShapingHandler());

        @SuppressWarnings("unchecked")
        MessageConfigManager<MessageConfig.MessageMeta> messageConfigMgr = Application.getBean(MessageConfigManager.class);
        // 使用带指标收集的编码器和解码器
        NetMessageEncoder messageEncoder = MetricsCodecFactory.createMetricsMessageEncoder(gcNetConfig, messageConfigMgr);
        NetMessageDecoder messageDecoder = MetricsCodecFactory.createMetricsMessageDecoder(gcNetConfig, messageConfigMgr);
        if (ServerConfigManager.getInstance().getGameConfig().isForCommandPlayer()) {
            ch.pipeline().addLast("GlobalTrafficShapingHandler", NettyTraffic.getGlobalTrafficShapingHandler())
                    .addLast("GlobalChannelTrafficShapingHandler", NettyTraffic.getGlobalChannelTrafficShapingHandler());
        }
        ch.pipeline().addLast("netMessageDecocer", messageDecoder)
                .addLast("netMesageEncoder", messageEncoder)
                .addLast("netHandler", new GcTopHandler())
                .addLast("inboundException", new ChannelHandler() {

                    @Override
                    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
                        ErrorLogUtil.exceptionLog("入站发生异常！",cause,"ctx",ctx);
                    }

                    @Override
                    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
                    }

                    @Override
                    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
                    }
                }).addLast("outboundException", new ChannelOutboundHandler() {

                    @Override
                    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
                        promise.addListener(new ChannelFutureListener() {

                            @Override
                            public void operationComplete(ChannelFuture future) throws Exception {
                                if (!future.isSuccess()) {
                                    handlerException(ctx, msg, promise, future.cause());
                                }
                            }
                        });
                        ctx.write(msg, promise);
                    }

                    protected void handlerException(ChannelHandlerContext ctx, Object msg, ChannelPromise promise,
                                                    Throwable t) {
                        MsgPack msgPack = (MsgPack) msg;
                        // StacklessClosedChannelException 和 Connection reset by peer 异常不打印
                        if (!t.getMessage().equals("Connection reset by peer")
                                && !(t instanceof ClosedChannelException)
                                && !t.getMessage().equals("Broken pipe")
                        ) {
                            ErrorLogUtil.exceptionLog("出站发生异常！",t,"ctx",ctx,"msg",msgPack,"promise",promise);
                        }
                    }

                    @Override
                    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
                    }

                    @Override
                    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
                    }

                    @Override
                    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
                        ctx.fireExceptionCaught(cause);
                    }

                    @Override
                    public void bind(ChannelHandlerContext ctx, SocketAddress localAddress, ChannelPromise promise)
                            throws Exception {
                        ctx.bind(localAddress, promise);
                    }

                    @Override
                    public void connect(ChannelHandlerContext ctx, SocketAddress remoteAddress,
                                        SocketAddress localAddress, ChannelPromise promise) throws Exception {
                        ctx.connect(remoteAddress, localAddress, promise);
                    }

                    @Override
                    public void disconnect(ChannelHandlerContext ctx, ChannelPromise promise) throws Exception {
                        ctx.disconnect(promise);
                    }

                    @Override
                    public void close(ChannelHandlerContext ctx, ChannelPromise promise) throws Exception {
                        ctx.close(promise);
                    }

                    @Override
                    public void deregister(ChannelHandlerContext ctx, ChannelPromise promise) throws Exception {
                        ctx.deregister(promise);
                    }

                    @Override
                    public void read(ChannelHandlerContext ctx) throws Exception {
                        ctx.read();
                    }

                    @Override
                    public void flush(ChannelHandlerContext ctx) throws Exception {
                        ctx.flush();
                    }
                });
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.3.xsd  
     http://www.springframework.org/schema/context  
     http://www.springframework.org/schema/context/spring-context-4.3.xsd 
     http://www.springframework.org/schema/aop 
     http://www.springframework.org/schema/aop/spring-aop-4.3.xsd
     http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.1.xsd">

	<bean id="messageConfigManager"
		class="com.lc.billion.icefire.game.msg.GameMessageConfigManager"
		init-method="init">
		<property name="configFileName" value="message-config.xml" />
		<property name="messagePackage"
			value="com.lc.billion.icefire.protocol" />
	</bean>

	<bean id="messageHandlerFactory"
		class="com.lc.billion.icefire.game.msg.GameMessageHandlerFactory"
		init-method="init">
		<property name="size" value="10000" />
		<property name="handlerParentPackage"
			value="com.lc.billion.icefire.game.msg.handler" />
		<property name="messageConfigManager"
			ref="messageConfigManager" />
	</bean>

	<bean id="globalMessageExecutor"
		class="com.lc.billion.icefire.game.msg.GlobalMessageExecutor" />

	<bean id="mainWorker"
		class="com.lc.billion.icefire.game.biz.exec.MainWorker" />

	<bean id="messageDispatcher"
		class="com.lc.billion.icefire.game.net.GcMessageDispatcher" />

	<bean id="configureService"
		class="com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl">
		<property name="configPackages">
			<list>
				<value>com.lc.billion.icefire.game.biz.config</value>
				<value>com.lc.billion.icefire.gvgbattle.biz.config</value>
				<value>com.lc.billion.icefire.gvgcontrol.biz.config</value>
				<value>com.lc.billion.icefire.csabattle.biz.config</value>
				<value>com.lc.billion.icefire.csacontrol.biz.config</value>
				<value>com.lc.billion.icefire.tvtControl.biz.config</value>
			</list>
		</property>
	</bean>

	<bean id = "configChangeListener"
		  class="com.lc.billion.icefire.game.biz.config.ConfigChangeListener"/>

	<bean id="configCenter"
		class="com.longtech.ls.zookeeper.ConfigCenter" destroy-method="close">
		<constructor-arg name="envKey_GameServerId"
			value="LS_ENV_KEY_GAMESERVER_ID" />
		<constructor-arg name="envKey_ZkPath"
			value="LS_ENV_KEY_ZK_PATH" />
		<constructor-arg name="envKey_ZkUrl"
			value="LS_ENV_KEY_ZK_URL" />
		<constructor-arg name="zkConnectString"
			value="#{serverConfigManager.zookeeperConfig.connectString}" />
		<constructor-arg name="base"
			value="#{serverConfigManager.zookeeperConfig.configPath}" />
		<constructor-arg name="serverTypeDeterminer">
			<ref bean="serverTypeDeterminer"/>
		</constructor-arg>
	</bean>

	<bean
		class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
		<property name="targetObject" ref="configCenter" />
		<property name="targetMethod"
			value="addGameServerConfigListener" />
		<property name="arguments" ref="gameConfigListener" />
	</bean>

	<bean id="gameConfigListener"
		class="com.lc.billion.icefire.game.biz.service.zk.GameConfigListener">
	</bean>

	<bean id="multiJongoClient"
		class="com.lc.billion.icefire.game.biz.dao.jongo.multi.MultiJongoClient" destroy-method="close">
		<constructor-arg name="dbUrls"
			value="#{configCenter.lsConfig.mongoDbUrls}" />
		<constructor-arg name="currentServerId"
						 value="#{configCenter.currentGameServerConfig.getGameServerId()}" />
		<constructor-arg name="serverTypeCfg"
						 value="#{serverConfigManager.serverTypeConfig}" />
	</bean>

	<!-- cache 配置,存在冗余属性，选择jedis时，codis的属性无效；同理，选择codis时，jedis的属性无效 -->
	<bean id="redisClient"
		class="com.lc.billion.icefire.game.biz.redis.JedisClient">
		<property name="host"
			value="#{configCenter.currentGameServerConfig.redis.host}" />
		<property name="port"
			value="#{configCenter.currentGameServerConfig.redis.port}" />
		<property name="password"
			value="#{configCenter.currentGameServerConfig.redis.password}" />
		<property name="timeout" value="30000" />
		<property name="maxTotal" value="50" />
		<property name="maxIdle" value="5" />
		<property name="mxWaitMillis" value="5000" />
		<property name="dbindex" value="#{configCenter.lsConfig.webServer.redis.dbIndex}"/>
	</bean>

	<!-- 老功能使用，暂时保留，不与深究 -->
	<bean id="webRedisClient0"
		class="com.lc.billion.icefire.game.biz.redis.WebJedisClient">
		<property name="host"
			value="#{configCenter.lsConfig.webServer.redis.host}" />
		<property name="port"
			value="#{configCenter.lsConfig.webServer.redis.port}" />
		<property name="password"
			value="#{configCenter.lsConfig.webServer.redis.password}" />
		<property name="timeout" value="30000" />
		<property name="maxTotal" value="50" />
		<property name="maxIdle" value="5" />
		<property name="mxWaitMillis" value="5000" />
		<property name="dbindex" value="0" />
	</bean>

	<!-- 活动中的多服排名，或者活动中的少量的多服务数据共享 -->
	<bean id="webRedisClient1"
		class="com.lc.billion.icefire.game.biz.redis.WebJedisClient">
		<property name="host"
			value="#{configCenter.lsConfig.webServer.redis.host}" />
		<property name="port"
			value="#{configCenter.lsConfig.webServer.redis.port}" />
		<property name="password"
			value="#{configCenter.lsConfig.webServer.redis.password}" />
		<property name="timeout" value="30000" />
		<property name="maxTotal" value="50" />
		<property name="maxIdle" value="5" />
		<property name="mxWaitMillis" value="5000" />
		<property name="dbindex" value="1" />
	</bean>

	<!-- 边缘功能中需要跨服共享的数据，如：公告点赞数（多服共享） -->
	<bean id="webRedisClient2"
		class="com.lc.billion.icefire.game.biz.redis.WebJedisClient">
		<property name="host"
			value="#{configCenter.lsConfig.webServer.redis.host}" />
		<property name="port"
			value="#{configCenter.lsConfig.webServer.redis.port}" />
		<property name="password"
			value="#{configCenter.lsConfig.webServer.redis.password}" />
		<property name="timeout" value="30000" />
		<property name="maxTotal" value="50" />
		<property name="maxIdle" value="5" />
		<property name="mxWaitMillis" value="5000" />
		<property name="dbindex" value="2" />
	</bean>

	<!-- GVG战斗服id记录在redis里，方便下次恢复，因为 -->
	<bean id="webRedisClient3"
		class="com.lc.billion.icefire.game.biz.redis.WebJedisClient">
		<property name="host"
			value="#{configCenter.lsConfig.webServer.redis.host}" />
		<property name="port"
			value="#{configCenter.lsConfig.webServer.redis.port}" />
		<property name="password"
			value="#{configCenter.lsConfig.webServer.redis.password}" />
		<property name="timeout" value="30000" />
		<property name="maxTotal" value="50" />
		<property name="maxIdle" value="5" />
		<property name="mxWaitMillis" value="5000" />
		<property name="dbindex" value="3" />
	</bean>


	<!-- 红包数据 -->
	<bean id="webRedisClient4"
		  class="com.lc.billion.icefire.game.biz.redis.WebJedisClient">
		<property name="host"
				  value="#{configCenter.lsConfig.webServer.redis.host}" />
		<property name="port"
				  value="#{configCenter.lsConfig.webServer.redis.port}" />
		<property name="password"
				  value="#{configCenter.lsConfig.webServer.redis.password}" />
		<property name="timeout" value="30000" />
		<property name="maxTotal" value="50" />
		<property name="maxIdle" value="5" />
		<property name="mxWaitMillis" value="5000" />
		<property name="dbindex" value="4" />
	</bean>

    <!-- 原zk的instance数据：每个服的注册速度、在线人数等 -->
    <bean id="webRedisClient5"
          class="com.lc.billion.icefire.game.biz.redis.WebJedisClient">
        <property name="host"
                  value="#{configCenter.lsConfig.webServer.redis.host}" />
        <property name="port"
                  value="#{configCenter.lsConfig.webServer.redis.port}" />
        <property name="password"
                  value="#{configCenter.lsConfig.webServer.redis.password}" />
        <property name="timeout" value="30000" />
        <property name="maxTotal" value="50" />
        <property name="maxIdle" value="5" />
        <property name="mxWaitMillis" value="5000" />
        <property name="dbindex" value="5" />
    </bean>

	<task:annotation-driven />

	<context:property-placeholder
		location="classpath:spring-conf.properties" />
	<context:component-scan
		base-package="com.lc.billion.icefire.game.support.status" />
	<context:component-scan
		base-package="com.lc.billion.icefire.game.biz.base" />
	<context:component-scan
			base-package="com.lc.billion.icefire.game.metrics"/>
	<context:component-scan
		base-package="com.lc.billion.icefire.core.config"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Service" />
	</context:component-scan>

	<context:component-scan
		base-package="com.lc.billion.icefire.game.config"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Service" />
	</context:component-scan>

	<context:component-scan
			base-package="com.lc.billion.icefire.game.biz.service,com.lc.billion.icefire.gvgcontrol.biz.service,com.lc.billion.icefire.gvgbattle.biz.service,com.lc.billion.icefire.kvkseason.biz.service,com.lc.billion.icefire.kvkcontrol.biz.service,com.lc.billion.icefire.csabattle.biz.service,com.lc.billion.icefire.csacontrol.biz.service,com.lc.billion.icefire.game.support.alert,com.lc.billion.icefire.tvtcontrol.biz.service"
			name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
			use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Service" />
	</context:component-scan>

	<context:component-scan
		base-package="com.lc.billion.icefire.game.msg.handler.impl,com.lc.billion.icefire.csabattle.msg.handler"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<context:component-scan
		base-package="com.lc.billion.icefire.game.biz.tick,com.lc.billion.icefire.gvgbattle.biz.tick,com.lc.billion.icefire.gvgcontrol.biz.tick,com.lc.billion.icefire.kvkseason.biz.tick,com.lc.billion.icefire.tvtcontrol.biz.tick"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Service" />
	</context:component-scan>

	<context:component-scan
		base-package="com.lc.billion.icefire.game.biz.battle,com.lc.billion.icefire.game.biz.manager,com.lc.billion.icefire.gvgbattle.biz.manager,com.lc.billion.icefire.gvgcontrol.biz.manager,com.lc.billion.icefire.kvkseason.biz.manager,com.lc.billion.icefire.tvtcontrol.biz.manager"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Component" />
	</context:component-scan>

	<!-- web service -->
	<context:component-scan
		base-package="com.lc.billion.icefire.game.ws"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Service" />
	</context:component-scan>

	<!--push -->
	<context:component-scan
		base-package="com.lc.billion.icefire.game.snspush"
		name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
		use-default-filters="false">
		<context:include-filter type="annotation"
			expression="org.springframework.stereotype.Service" />
	</context:component-scan>

	<!-- aop -->
	<aop:aspectj-autoproxy proxy-target-class="true" />
    <!-- <bean targetId="allianceAdvice" class="com.lc.billion.icefire.game.biz.service.impl.alliance.interceptor.AllianceAdvice"
        /> -->
    <!--<bean targetId="wsAdvice" class="com.lc.billion.icefire.game.ws.impl.WsAdvice"
        /> -->

	<!--<aop:config> -->
    <!-- <aop:aspect targetId="allianceAspect" ref="allianceAdvice"> <aop:around method="execute"
        pointcut="execution(* com.lc.billion.icefire.game.biz.service.impl.alliance.*.*(..)
        ) and @annotation(com.lc.billion.icefire.game.biz.model.alliance.RankAuth)"
        /> </aop:aspect> -->
    <!-- <aop:aspect targetId="gmWsAspect" ref="wsAdvice"> <aop:around pointcut="execution(*
        com.lc.billion.icefire.game.ws.impl.GmWsImpl.*(..) ) and !@annotation(com.lc.billion.icefire.game.ws.DirectExecute)"
        method="execute" /> </aop:aspect> </aop:config> -->

	<context:component-scan
		base-package="com.lc.billion.icefire.game.biz.globalconfig" />

	<!-- 监控配置扫描 -->
	<context:component-scan
			base-package="com.lc.billion.icefire.configuration"
			name-generator="com.simfun.sgf.common.spring.SpringPackageBeanNameGenerator"
			use-default-filters="false">
		<context:include-filter type="annotation"
								expression="org.springframework.context.annotation.Configuration"/>
	</context:component-scan>
</beans>

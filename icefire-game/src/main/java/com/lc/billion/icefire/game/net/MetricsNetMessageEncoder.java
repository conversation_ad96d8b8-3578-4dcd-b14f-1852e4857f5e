package com.lc.billion.icefire.game.net;

import com.lc.billion.icefire.game.metrics.GamePacketMetrics;
import com.lc.billion.icefire.game.metrics.GamePacketMetricsConfig;
import com.simfun.sgf.monitoring.registry.MeterRegistryManager;
import com.simfun.sgf.net.MsgPack;
import com.simfun.sgf.net.NetConfig;
import com.simfun.sgf.net.NetMessageEncoder;
import com.simfun.sgf.net.codec.MessageBodyEncoder;
import com.simfun.sgf.net.compress.MessageBodyCompressor;
import com.simfun.sgf.net.msg.MessageConfig;
import com.simfun.sgf.net.msg.MessageConfigManager;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.websocketx.BinaryWebSocketFrame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 带指标收集的 NetMessage 编码器
 */
public class MetricsNetMessageEncoder extends NetMessageEncoder {

    private static final Logger log = LoggerFactory.getLogger(MetricsNetMessageEncoder.class);

    private final GamePacketMetrics gamePacketMetrics;
    private final MessageConfigManager<MessageConfig.MessageMeta> messageConfigManager;

    public MetricsNetMessageEncoder(
            NetConfig netConfig,
            MessageConfigManager<MessageConfig.MessageMeta> messageConfigManager,
            MessageBodyEncoder bodyEncoder,
            MessageBodyCompressor bodyCompressor,
            MeterRegistryManager meterRegistryManager,
            GamePacketMetricsConfig metricsConfig) {
        super(netConfig, messageConfigManager, bodyEncoder, bodyCompressor);
        this.gamePacketMetrics = new GamePacketMetrics(meterRegistryManager, metricsConfig);
        this.messageConfigManager = messageConfigManager;
    }

    public MetricsNetMessageEncoder(
            NetConfig netConfig,
            MessageConfigManager<MessageConfig.MessageMeta> messageConfigManager,
            MessageBodyEncoder bodyEncoder,
            MessageBodyCompressor bodyCompressor,
            int compressThreshold,
            MeterRegistryManager meterRegistryManager,
            GamePacketMetricsConfig metricsConfig) {
        super(netConfig, messageConfigManager, bodyEncoder, bodyCompressor, compressThreshold);
        this.gamePacketMetrics = new GamePacketMetrics(meterRegistryManager, metricsConfig);
        this.messageConfigManager = messageConfigManager;
    }

    @Override
    protected void encode(ChannelHandlerContext ctx, MsgPack msgPack, List<Object> out) throws Exception {
        // 调用父类编码逻辑
        super.encode(ctx, msgPack, out);
        
        //编码成功，则记录指标
        if (!out.isEmpty()) {
            Object lastEncoded = out.getLast();
            if (lastEncoded instanceof BinaryWebSocketFrame frame) {
                recordOutboundMetrics(msgPack, frame.content().readableBytes());
            }
        }
    }

    /**
     * 记录出站消息指标
     */
    private void recordOutboundMetrics(MsgPack msgPack, int totalLength) {
        try {
            var msg = msgPack.getMsg();
            var msgType = messageConfigManager.getMessageType(msg.getClass());
            var packetInfo = new GamePacketMetrics.PacketInfo(
                    false,       // inbound = false
                    totalLength, // 消息总长度（包含头部）
                    Integer.toString(msgType)
            );
            
            gamePacketMetrics.record(packetInfo);
            
            if (log.isDebugEnabled()) {
                log.debug("记录出站消息指标: type={}, length={}", msgType, totalLength);
            }
        } catch (Exception e) {
            log.warn("记录出站消息指标时发生异常", e);
        }
    }

}
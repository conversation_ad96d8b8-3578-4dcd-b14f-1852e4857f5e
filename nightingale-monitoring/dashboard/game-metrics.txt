# HELP game_async_process_counter_total game async process count
# TYPE game_async_process_counter_total counter
game_async_process_counter_total{application="icefire-game",process_id="AbstractActivityHandler$1"} 1.0
game_async_process_counter_total{application="icefire-game",process_id="AbstractActivityHandler$2"} 1.0
game_async_process_counter_total{application="icefire-game",process_id="AbstractMemoryDao$4"} 14.0
game_async_process_counter_total{application="icefire-game",process_id="AlertService$1"} 4.0
game_async_process_counter_total{application="icefire-game",process_id="AlliancePushOperation"} 2.0
game_async_process_counter_total{application="icefire-game",process_id="AllianceServiceImpl$$Lambda/0x000001aa41025698"} 4.0
game_async_process_counter_total{application="icefire-game",process_id="ArenaCheckOperation"} 4.0
game_async_process_counter_total{application="icefire-game",process_id="CaravanMatchService$$Lambda/0x000001aa410be6b0"} 34.0
game_async_process_counter_total{application="icefire-game",process_id="ExcellentMarkService$1"} 1.0
game_async_process_counter_total{application="icefire-game",process_id="FightPowerServiceImpl$1"} 633.0
game_async_process_counter_total{application="icefire-game",process_id="NpcSceneNodeRefreshOperation"} 1.0
game_async_process_counter_total{application="icefire-game",process_id="RefreshRankTitleOperation"} 1.0
game_async_process_counter_total{application="icefire-game",process_id="RegionCapitalHotConflictOperation"} 2.0
game_async_process_counter_total{application="icefire-game",process_id="ResourceSceneNodeRefreshOperation"} 10.0
game_async_process_counter_total{application="icefire-game",process_id="WorldBossDongZhuoServiceImpl$1"} 1.0
game_async_process_counter_total{application="icefire-game",process_id="WorldBossNodeTicker$1"} 6330.0
# HELP game_async_process_timer_seconds game async process duration
# TYPE game_async_process_timer_seconds summary
game_async_process_timer_seconds_count{application="icefire-game",process_id="AbstractActivityHandler$1"} 1
game_async_process_timer_seconds_sum{application="icefire-game",process_id="AbstractActivityHandler$1"} 5.023E-4
game_async_process_timer_seconds_count{application="icefire-game",process_id="AbstractActivityHandler$2"} 1
game_async_process_timer_seconds_sum{application="icefire-game",process_id="AbstractActivityHandler$2"} 3.31E-5
game_async_process_timer_seconds_count{application="icefire-game",process_id="AbstractMemoryDao$4"} 14
game_async_process_timer_seconds_sum{application="icefire-game",process_id="AbstractMemoryDao$4"} 0.1061196
game_async_process_timer_seconds_count{application="icefire-game",process_id="AlertService$1"} 4
game_async_process_timer_seconds_sum{application="icefire-game",process_id="AlertService$1"} 1.319E-4
game_async_process_timer_seconds_count{application="icefire-game",process_id="AlliancePushOperation"} 2
game_async_process_timer_seconds_sum{application="icefire-game",process_id="AlliancePushOperation"} 0.0104228
game_async_process_timer_seconds_count{application="icefire-game",process_id="AllianceServiceImpl$$Lambda/0x000001aa41025698"} 4
game_async_process_timer_seconds_sum{application="icefire-game",process_id="AllianceServiceImpl$$Lambda/0x000001aa41025698"} 0.0152816
game_async_process_timer_seconds_count{application="icefire-game",process_id="ArenaCheckOperation"} 4
game_async_process_timer_seconds_sum{application="icefire-game",process_id="ArenaCheckOperation"} 0.0130155
game_async_process_timer_seconds_count{application="icefire-game",process_id="CaravanMatchService$$Lambda/0x000001aa410be6b0"} 34
game_async_process_timer_seconds_sum{application="icefire-game",process_id="CaravanMatchService$$Lambda/0x000001aa410be6b0"} 4.482E-4
game_async_process_timer_seconds_count{application="icefire-game",process_id="ExcellentMarkService$1"} 1
game_async_process_timer_seconds_sum{application="icefire-game",process_id="ExcellentMarkService$1"} 0.0052614
game_async_process_timer_seconds_count{application="icefire-game",process_id="FightPowerServiceImpl$1"} 633
game_async_process_timer_seconds_sum{application="icefire-game",process_id="FightPowerServiceImpl$1"} 0.0164539
game_async_process_timer_seconds_count{application="icefire-game",process_id="NpcSceneNodeRefreshOperation"} 1
game_async_process_timer_seconds_sum{application="icefire-game",process_id="NpcSceneNodeRefreshOperation"} 5.612E-4
game_async_process_timer_seconds_count{application="icefire-game",process_id="RefreshRankTitleOperation"} 1
game_async_process_timer_seconds_sum{application="icefire-game",process_id="RefreshRankTitleOperation"} 0.0098974
game_async_process_timer_seconds_count{application="icefire-game",process_id="RegionCapitalHotConflictOperation"} 2
game_async_process_timer_seconds_sum{application="icefire-game",process_id="RegionCapitalHotConflictOperation"} 6.67E-5
game_async_process_timer_seconds_count{application="icefire-game",process_id="ResourceSceneNodeRefreshOperation"} 10
game_async_process_timer_seconds_sum{application="icefire-game",process_id="ResourceSceneNodeRefreshOperation"} 0.0092387
game_async_process_timer_seconds_count{application="icefire-game",process_id="WorldBossDongZhuoServiceImpl$1"} 1
game_async_process_timer_seconds_sum{application="icefire-game",process_id="WorldBossDongZhuoServiceImpl$1"} 9.75E-5
game_async_process_timer_seconds_count{application="icefire-game",process_id="WorldBossNodeTicker$1"} 6330
game_async_process_timer_seconds_sum{application="icefire-game",process_id="WorldBossNodeTicker$1"} 0.1787997
# HELP game_async_process_timer_seconds_max game async process duration
# TYPE game_async_process_timer_seconds_max gauge
game_async_process_timer_seconds_max{application="icefire-game",process_id="AbstractActivityHandler$1"} 5.023E-4
game_async_process_timer_seconds_max{application="icefire-game",process_id="AbstractActivityHandler$2"} 3.31E-5
game_async_process_timer_seconds_max{application="icefire-game",process_id="AbstractMemoryDao$4"} 0.0258567
game_async_process_timer_seconds_max{application="icefire-game",process_id="AlertService$1"} 4.3E-5
game_async_process_timer_seconds_max{application="icefire-game",process_id="AlliancePushOperation"} 0.005288
game_async_process_timer_seconds_max{application="icefire-game",process_id="AllianceServiceImpl$$Lambda/0x000001aa41025698"} 0.0073114
game_async_process_timer_seconds_max{application="icefire-game",process_id="ArenaCheckOperation"} 0.0042489
game_async_process_timer_seconds_max{application="icefire-game",process_id="CaravanMatchService$$Lambda/0x000001aa410be6b0"} 5.26E-5
game_async_process_timer_seconds_max{application="icefire-game",process_id="ExcellentMarkService$1"} 0.0052614
game_async_process_timer_seconds_max{application="icefire-game",process_id="FightPowerServiceImpl$1"} 1.35E-4
game_async_process_timer_seconds_max{application="icefire-game",process_id="NpcSceneNodeRefreshOperation"} 5.612E-4
game_async_process_timer_seconds_max{application="icefire-game",process_id="RefreshRankTitleOperation"} 0.0098974
game_async_process_timer_seconds_max{application="icefire-game",process_id="RegionCapitalHotConflictOperation"} 4.31E-5
game_async_process_timer_seconds_max{application="icefire-game",process_id="ResourceSceneNodeRefreshOperation"} 0.0032366
game_async_process_timer_seconds_max{application="icefire-game",process_id="WorldBossDongZhuoServiceImpl$1"} 9.75E-5
game_async_process_timer_seconds_max{application="icefire-game",process_id="WorldBossNodeTicker$1"} 0.0026731
# HELP game_dao_memory_total memory dao
# TYPE game_dao_memory_total counter
game_dao_memory_total{application="icefire-game",entityClass="AbstractEmail",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AbstractEmail",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AbstractEmail",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AchieveInfoRecord",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="AchieveInfoRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AchieveInfoRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AchieveTaskRecord",op="create"} 138.0
game_dao_memory_total{application="icefire-game",entityClass="AchieveTaskRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AchieveTaskRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Activity",op="create"} 25.0
game_dao_memory_total{application="icefire-game",entityClass="Activity",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Activity",op="update"} 3.0
game_dao_memory_total{application="icefire-game",entityClass="ActivityRecord",op="create"} 11.0
game_dao_memory_total{application="icefire-game",entityClass="ActivityRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ActivityRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ActivityTurn",op="create"} 30.0
game_dao_memory_total{application="icefire-game",entityClass="ActivityTurn",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ActivityTurn",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Alliance",op="create"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="Alliance",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Alliance",op="update"} 14.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceAffair",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceAffair",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceAffair",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBaseData",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBaseData",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBaseData",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBattleInfo",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBattleInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBattleInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBattlePlayerInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBattlePlayerInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBattlePlayerInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBattlePoint",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBattlePoint",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBattlePoint",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBossNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBossNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBossNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBossRoleDonateInfo",op="create"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBossRoleDonateInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBossRoleDonateInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBuildingBase",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBuildingBase",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBuildingBase",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBuildingNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBuildingNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBuildingNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBuildingTech",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBuildingTech",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceBuildingTech",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceCommonActivityInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceCommonActivityInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceCommonActivityInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceCommonRankSnap",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceCommonRankSnap",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceCommonRankSnap",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceFeastHallNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceFeastHallNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceFeastHallNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceFeastNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceFeastNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceFeastNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceGift",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceGift",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceGift",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceLeaderMission",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceLeaderMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceLeaderMission",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceLog",op="create"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceLog",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceLog",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMark",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMark",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMark",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMember",op="create"} 3.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMember",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMember",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMessageBord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMessageBord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMessageBord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMissionBase",op="create"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMissionBase",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMissionBase",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMissionMemberInfo",op="create"} 3.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMissionMemberInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceMissionMemberInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceOpRecord",op="create"} 3.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceOpRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceOpRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceReplace",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceReplace",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceReplace",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceRequest",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceRequest",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceRequest",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceSeasonTaskProgress",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceSeasonTaskProgress",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceSeasonTaskProgress",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceSetting",op="create"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceSetting",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceSetting",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceTech",op="create"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceTech",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceTech",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceWar",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceWar",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceWar",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceWhispererActivityRecord",op="create"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceWhispererActivityRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="AllianceWhispererActivityRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ArmyInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ArmyInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ArmyInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ArmyPreprogrammed",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ArmyPreprogrammed",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ArmyPreprogrammed",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ArmyPresetInfo",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="ArmyPresetInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ArmyPresetInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="BarbarianCityNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="BarbarianCityNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="BarbarianCityNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="BingoActivityContext",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="BingoActivityContext",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="BingoActivityContext",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Buff",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="Buff",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Buff",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAActivityHistory",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAActivityHistory",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAActivityHistory",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAGameServerResult",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAGameServerResult",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAGameServerResult",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAServerBattleInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAServerBattleInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAServerBattleInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAServerTrophy",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAServerTrophy",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CSAServerTrophy",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Caravan",op="create"} 5.0
game_dao_memory_total{application="icefire-game",entityClass="Caravan",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Caravan",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CaravanTrade",op="create"} 26.0
game_dao_memory_total{application="icefire-game",entityClass="CaravanTrade",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CaravanTrade",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CityBuild",op="create"} 180.0
game_dao_memory_total{application="icefire-game",entityClass="CityBuild",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CityBuild",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CommonMissionInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CommonMissionInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="CommonMissionInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="DongzhuoBossBox",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="DongzhuoBossBox",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="DongzhuoBossBox",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGActivityTurn",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGActivityTurn",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGActivityTurn",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGAdmissionPush",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGAdmissionPush",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGAdmissionPush",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGAllianceBattleSummary",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGAllianceBattleSummary",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGAllianceBattleSummary",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGAllianceSignUpInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGAllianceSignUpInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGAllianceSignUpInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGBattleFieldTimeLine",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGBattleFieldTimeLine",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGBattleFieldTimeLine",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGBattleRecord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGBattleRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGBattleRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGMatchResultBackup",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGMatchResultBackup",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGMatchResultBackup",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGQualifiedAlliance",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGQualifiedAlliance",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGQualifiedAlliance",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGQualifiedServer",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGQualifiedServer",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGQualifiedServer",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGRoleArenaInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGRoleArenaInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGRoleArenaInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGRoleInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGRoleInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGRoleInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGRoleWillStatusInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGRoleWillStatusInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GVGRoleWillStatusInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgAllianceRecord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgAllianceRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgAllianceRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgBattleServerDispatchRecord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgBattleServerDispatchRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgBattleServerDispatchRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgCupApplyInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgCupApplyInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgCupApplyInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgNpcNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgNpcNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgNpcNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgObserveMatch",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgObserveMatch",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgObserveMatch",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgRegisterInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgRegisterInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgRegisterInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgResNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgResNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="GvgResNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Hero",op="create"} 39.0
game_dao_memory_total{application="icefire-game",entityClass="Hero",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Hero",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="HorseMating",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="HorseMating",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="HorseMating",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="IceRoleWheelInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="IceRoleWheelInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="IceRoleWheelInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ImportReward",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ImportReward",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ImportReward",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="IntegratedSpringBoard",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="IntegratedSpringBoard",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="IntegratedSpringBoard",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Item",op="create"} 152.0
game_dao_memory_total{application="icefire-game",entityClass="Item",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Item",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ItemGuarantee",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ItemGuarantee",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ItemGuarantee",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ItemRecord",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="ItemRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ItemRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KingdomBuff",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KingdomBuff",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KingdomBuff",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHistoryAllianceProsperity",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHistoryAllianceProsperity",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHistoryAllianceProsperity",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHistoryLegionProsperity",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHistoryLegionProsperity",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHistoryLegionProsperity",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorAlliance",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorAlliance",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorAlliance",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorLegion",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorLegion",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorLegion",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorRole",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorRole",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorRole",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorServer",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorServer",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkHonorServer",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkSeasonServerGroup",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkSeasonServerGroup",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="KvkSeasonServerGroup",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Legion",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Legion",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Legion",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionLog",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionLog",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionLog",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionMark",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionMark",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionMark",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionMember",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionMember",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionMember",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionMilestone",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionMilestone",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionMilestone",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionOfficial",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionOfficial",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionOfficial",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionRequest",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionRequest",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionRequest",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionSeasonTaskProgress",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionSeasonTaskProgress",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LegionSeasonTaskProgress",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LoginRewardRecord",op="create"} 5.0
game_dao_memory_total{application="icefire-game",entityClass="LoginRewardRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="LoginRewardRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Milestone",op="create"} 26.0
game_dao_memory_total{application="icefire-game",entityClass="Milestone",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Milestone",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="MissionBossNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="MissionBossNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="MissionBossNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NewPlayerMission",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="NewPlayerMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NewPlayerMission",op="update"} 127.0
game_dao_memory_total{application="icefire-game",entityClass="NewResNode",op="create"} 61813.0
game_dao_memory_total{application="icefire-game",entityClass="NewResNode",op="delete"} 5475.0
game_dao_memory_total{application="icefire-game",entityClass="NewResNode",op="update"} 2388.0
game_dao_memory_total{application="icefire-game",entityClass="NewStrongestLordsPlan",op="create"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="NewStrongestLordsPlan",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NewStrongestLordsPlan",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NewStrongestPlayerInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NewStrongestPlayerInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NewStrongestPlayerInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NpcCenterNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NpcCenterNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NpcCenterNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="NpcNode",op="create"} 18632.0
game_dao_memory_total{application="icefire-game",entityClass="NpcNode",op="delete"} 253.0
game_dao_memory_total{application="icefire-game",entityClass="NpcNode",op="update"} 240.0
game_dao_memory_total{application="icefire-game",entityClass="OfficialsInfo",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="OfficialsInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="OfficialsInfo",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="OnlineRewardData",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="OnlineRewardData",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="OnlineRewardData",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="PersonalMilestone",op="create"} 4.0
game_dao_memory_total{application="icefire-game",entityClass="PersonalMilestone",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PersonalMilestone",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerChapterMission",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerChapterMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerChapterMission",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerCommonActivityInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerCommonActivityInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerCommonActivityInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerCommonRankSnap",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerCommonRankSnap",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerCommonRankSnap",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerDailyMission",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerDailyMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerDailyMission",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerFeederMission",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerFeederMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerFeederMission",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerMainMission",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerMainMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerMainMission",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerPopularWillMission",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerPopularWillMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerPopularWillMission",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerPushInfo",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerPushInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerPushInfo",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerShareMission",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerShareMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerShareMission",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerSnsPushInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerSnsPushInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerSnsPushInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerWorldExploreEvent",op="create"} 28.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerWorldExploreEvent",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerWorldExploreEvent",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerWorldExploreRecord",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerWorldExploreRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="PlayerWorldExploreRecord",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RZEAllianceInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RZEAllianceInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RZEAllianceInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RZEQualifiedServer",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RZEQualifiedServer",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RZEQualifiedServer",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RZERoomInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RZERoomInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RZERoomInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RedPack",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RedPack",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RedPack",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RegionCapitalNode",op="create"} 89.0
game_dao_memory_total{application="icefire-game",entityClass="RegionCapitalNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RegionCapitalNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RegionCapitalRecord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RegionCapitalRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RegionCapitalRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ResourceOutput",op="create"} 36.0
game_dao_memory_total{application="icefire-game",entityClass="ResourceOutput",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ResourceOutput",op="update"} 125.0
game_dao_memory_total{application="icefire-game",entityClass="Role",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="Role",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Role",op="update"} 4.0
game_dao_memory_total{application="icefire-game",entityClass="RoleABStamp",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleABStamp",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleABStamp",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleActivityMission",op="create"} 145.0
game_dao_memory_total{application="icefire-game",entityClass="RoleActivityMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleActivityMission",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleAllianceRecord",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleAllianceRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleAllianceRecord",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleArena",op="create"} 4.0
game_dao_memory_total{application="icefire-game",entityClass="RoleArena",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleArena",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleAttackDongzhuoBossRecord",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleAttackDongzhuoBossRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleAttackDongzhuoBossRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBattlePass",op="create"} 4.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBattlePass",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBattlePass",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBlizzard",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBlizzard",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBlizzard",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBlizzardResist",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBlizzardResist",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBlizzardResist",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBrotherHoodInfo",op="create"} 3.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBrotherHoodInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBrotherHoodInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBuildSearchProgress",op="create"} 4.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBuildSearchProgress",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleBuildSearchProgress",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCache",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCache",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCache",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCity",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCity",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCity",op="update"} 3.0
game_dao_memory_total{application="icefire-game",entityClass="RoleContinuousRecharge",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleContinuousRecharge",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleContinuousRecharge",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCreateZoneGridIndex",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCreateZoneGridIndex",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCreateZoneGridIndex",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCsaBattle",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCsaBattle",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCsaBattle",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCsaMission",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCsaMission",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleCsaMission",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleDailyRecharge",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleDailyRecharge",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleDailyRecharge",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleDevice",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleDevice",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleDevice",op="update"} 3.0
game_dao_memory_total{application="icefire-game",entityClass="RoleEnlistment",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleEnlistment",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleEnlistment",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleEquip",op="create"} 3.0
game_dao_memory_total{application="icefire-game",entityClass="RoleEquip",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleEquip",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExpedition",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExpedition",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExpedition",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExpeditionMul",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExpeditionMul",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExpeditionMul",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExpeditionNumberGate",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExpeditionNumberGate",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExpeditionNumberGate",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExtra",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExtra",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleExtra",op="update"} 19.0
game_dao_memory_total{application="icefire-game",entityClass="RoleFreeTreasureBox",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleFreeTreasureBox",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleFreeTreasureBox",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleGVGBattle",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleGVGBattle",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleGVGBattle",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleGVGExtraInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleGVGExtraInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleGVGExtraInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleGuide",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleGuide",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleGuide",op="update"} 12.0
game_dao_memory_total{application="icefire-game",entityClass="RoleHeadInfo",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleHeadInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleHeadInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleHorse",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleHorse",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleHorse",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLibaoFilterInfo",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLibaoFilterInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLibaoFilterInfo",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLibaoRecord",op="create"} 412.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLibaoRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLibaoRecord",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLordTreasure",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLordTreasure",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLordTreasure",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLottery",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLottery",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleLottery",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleMigrateRecord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleMigrateRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleMigrateRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleMonthCard",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleMonthCard",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleMonthCard",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RolePeople",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RolePeople",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RolePeople",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RolePopularSkill",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RolePopularSkill",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RolePopularSkill",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RolePopularWill",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RolePopularWill",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RolePopularWill",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRechargeLog",op="create"} 18.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRechargeLog",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRechargeLog",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRecord",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRecord",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRegionCapital",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRegionCapital",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRegionCapital",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRobber",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRobber",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleRobber",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSeasonInfo",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSeasonInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSeasonInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSeasonTaskRecord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSeasonTaskRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSeasonTaskRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleServerInfo",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleServerInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleServerInfo",op="update"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSetting",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSetting",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSetting",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSevenDayRecharge",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSevenDayRecharge",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleSevenDayRecharge",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleShare",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleShare",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleShare",op="update"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="RoleStore",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleStore",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleStore",op="update"} 3.0
game_dao_memory_total{application="icefire-game",entityClass="RoleTitles",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleTitles",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleTitles",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleUnlock",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleUnlock",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleUnlock",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="RoleVip",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleVip",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleVip",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleWhispererActivityRecord",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="RoleWhispererActivityRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RoleWhispererActivityRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RuinsNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RuinsNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="RuinsNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ScienceInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ScienceInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ScienceInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SeasonWarmUpDynastyInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SeasonWarmUpDynastyInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SeasonWarmUpDynastyInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SeasonWarmUpPlayerInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SeasonWarmUpPlayerInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SeasonWarmUpPlayerInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ServerInfo",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="ServerInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ServerInfo",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="ServerMilestones",op="create"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="ServerMilestones",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ServerMilestones",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ServerRecord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ServerRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ServerRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ServerSeasonTaskProgress",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ServerSeasonTaskProgress",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ServerSeasonTaskProgress",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SevenCaptureDailyInfo",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="SevenCaptureDailyInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SevenCaptureDailyInfo",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="SevenCaptureNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SevenCaptureNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SevenCaptureNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ShieldRecord",op="create"} 12.0
game_dao_memory_total{application="icefire-game",entityClass="ShieldRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="ShieldRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SiegeEnginesNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SiegeEnginesNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SiegeEnginesNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SkinData",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="SkinData",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SkinData",op="update"} 2.0
game_dao_memory_total{application="icefire-game",entityClass="SoldierInfo",op="create"} 6.0
game_dao_memory_total{application="icefire-game",entityClass="SoldierInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="SoldierInfo",op="update"} 1.0
game_dao_memory_total{application="icefire-game",entityClass="StrongHoldNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="StrongHoldNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="StrongHoldNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Trusteeship",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Trusteeship",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="Trusteeship",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtBattleServerDispatchRecord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtBattleServerDispatchRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtBattleServerDispatchRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtPlayerSignupInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtPlayerSignupInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtPlayerSignupInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtPlayerSimpleInfo",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtPlayerSimpleInfo",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtPlayerSimpleInfo",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtQualifiedServer",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtQualifiedServer",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="TvtQualifiedServer",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="UnActiveRoleMailRecord",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="UnActiveRoleMailRecord",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="UnActiveRoleMailRecord",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WhispererBossNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WhispererBossNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WhispererBossNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WhispererNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WhispererNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WhispererNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WorkProgress",op="create"} 29.0
game_dao_memory_total{application="icefire-game",entityClass="WorkProgress",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WorkProgress",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WorldBossDongzhuoNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WorldBossDongzhuoNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WorldBossDongzhuoNode",op="update"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WorldBossNode",op="create"} 10151.0
game_dao_memory_total{application="icefire-game",entityClass="WorldBossNode",op="delete"} 5744.0
game_dao_memory_total{application="icefire-game",entityClass="WorldBossNode",op="update"} 3947.0
game_dao_memory_total{application="icefire-game",entityClass="WorldCastleNode",op="create"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WorldCastleNode",op="delete"} 0.0
game_dao_memory_total{application="icefire-game",entityClass="WorldCastleNode",op="update"} 0.0
# HELP game_dao_memory_size memory dao size
# TYPE game_dao_memory_size gauge
game_dao_memory_size{application="icefire-game",entityClass="AbstractEmail"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AchieveInfoRecord"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="AchieveTaskRecord"} 138.0
game_dao_memory_size{application="icefire-game",entityClass="Activity"} 25.0
game_dao_memory_size{application="icefire-game",entityClass="ActivityRecord"} 11.0
game_dao_memory_size{application="icefire-game",entityClass="ActivityTurn"} 30.0
game_dao_memory_size{application="icefire-game",entityClass="Alliance"} 2.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceAffair"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceBaseData"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceBattleInfo"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceBattlePlayerInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceBattlePoint"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceBossNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceBossRoleDonateInfo"} 2.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceBuildingBase"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceBuildingNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceBuildingTech"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceCommonActivityInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceCommonRankSnap"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceFeastHallNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceFeastNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceGift"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceLeaderMission"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceLog"} 2.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceMark"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceMember"} 3.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceMessageBord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceMissionBase"} 2.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceMissionMemberInfo"} 3.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceOpRecord"} 3.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceReplace"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceRequest"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceSeasonTaskProgress"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceSetting"} 2.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceTech"} 2.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceWar"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="AllianceWhispererActivityRecord"} 2.0
game_dao_memory_size{application="icefire-game",entityClass="ArmyInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="ArmyPreprogrammed"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="ArmyPresetInfo"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="BarbarianCityNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="BingoActivityContext"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="Buff"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="CSAActivityHistory"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="CSAGameServerResult"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="CSAServerBattleInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="CSAServerTrophy"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="Caravan"} 5.0
game_dao_memory_size{application="icefire-game",entityClass="CaravanTrade"} 26.0
game_dao_memory_size{application="icefire-game",entityClass="CityBuild"} 180.0
game_dao_memory_size{application="icefire-game",entityClass="CommonMissionInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="DongzhuoBossBox"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGActivityTurn"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGAdmissionPush"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGAllianceBattleSummary"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGAllianceSignUpInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGBattleFieldTimeLine"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGBattleRecord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGMatchResultBackup"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGQualifiedAlliance"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGQualifiedServer"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGRoleArenaInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGRoleInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GVGRoleWillStatusInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GvgAllianceRecord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GvgBattleServerDispatchRecord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GvgCupApplyInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GvgNpcNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GvgObserveMatch"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GvgRegisterInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="GvgResNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="Hero"} 39.0
game_dao_memory_size{application="icefire-game",entityClass="HorseMating"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="IceRoleWheelInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="ImportReward"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="IntegratedSpringBoard"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="Item"} 152.0
game_dao_memory_size{application="icefire-game",entityClass="ItemGuarantee"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="ItemRecord"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="KingdomBuff"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="KvkHistoryAllianceProsperity"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="KvkHistoryLegionProsperity"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="KvkHonorAlliance"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="KvkHonorLegion"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="KvkHonorRole"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="KvkHonorServer"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="KvkSeasonServerGroup"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="Legion"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="LegionLog"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="LegionMark"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="LegionMember"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="LegionMilestone"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="LegionOfficial"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="LegionRequest"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="LegionSeasonTaskProgress"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="LoginRewardRecord"} 5.0
game_dao_memory_size{application="icefire-game",entityClass="Milestone"} 26.0
game_dao_memory_size{application="icefire-game",entityClass="MissionBossNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="NewPlayerMission"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="NewResNode"} 56338.0
game_dao_memory_size{application="icefire-game",entityClass="NewStrongestLordsPlan"} 2.0
game_dao_memory_size{application="icefire-game",entityClass="NewStrongestPlayerInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="NpcCenterNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="NpcNode"} 18379.0
game_dao_memory_size{application="icefire-game",entityClass="OfficialsInfo"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="OnlineRewardData"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="PersonalMilestone"} 4.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerChapterMission"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerCommonActivityInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerCommonRankSnap"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerDailyMission"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerFeederMission"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerMainMission"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerPopularWillMission"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerPushInfo"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerShareMission"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerSnsPushInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerWorldExploreEvent"} 28.0
game_dao_memory_size{application="icefire-game",entityClass="PlayerWorldExploreRecord"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RZEAllianceInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RZEQualifiedServer"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RZERoomInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RedPack"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RegionCapitalNode"} 89.0
game_dao_memory_size{application="icefire-game",entityClass="RegionCapitalRecord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="ResourceOutput"} 36.0
game_dao_memory_size{application="icefire-game",entityClass="Role"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleABStamp"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleActivityMission"} 145.0
game_dao_memory_size{application="icefire-game",entityClass="RoleAllianceRecord"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleArena"} 4.0
game_dao_memory_size{application="icefire-game",entityClass="RoleAttackDongzhuoBossRecord"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="RoleBattlePass"} 4.0
game_dao_memory_size{application="icefire-game",entityClass="RoleBlizzard"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleBlizzardResist"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleBrotherHoodInfo"} 3.0
game_dao_memory_size{application="icefire-game",entityClass="RoleBuildSearchProgress"} 4.0
game_dao_memory_size{application="icefire-game",entityClass="RoleCache"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RoleCity"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleContinuousRecharge"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleCreateZoneGridIndex"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="RoleCsaBattle"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RoleCsaMission"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RoleDailyRecharge"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RoleDevice"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleEnlistment"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleEquip"} 3.0
game_dao_memory_size{application="icefire-game",entityClass="RoleExpedition"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleExpeditionMul"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleExpeditionNumberGate"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="RoleExtra"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleFreeTreasureBox"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleGVGBattle"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RoleGVGExtraInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RoleGuide"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleHeadInfo"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleHorse"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleLibaoFilterInfo"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleLibaoRecord"} 412.0
game_dao_memory_size{application="icefire-game",entityClass="RoleLordTreasure"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleLottery"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleMigrateRecord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RoleMonthCard"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="RolePeople"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RolePopularSkill"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RolePopularWill"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleRechargeLog"} 18.0
game_dao_memory_size{application="icefire-game",entityClass="RoleRecord"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleRegionCapital"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleRobber"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleSeasonInfo"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleSeasonTaskRecord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RoleServerInfo"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleSetting"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleSevenDayRecharge"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="RoleShare"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleStore"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleTitles"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleUnlock"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleVip"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RoleWhispererActivityRecord"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="RuinsNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="ScienceInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="SeasonWarmUpDynastyInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="SeasonWarmUpPlayerInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="ServerInfo"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="ServerMilestones"} 1.0
game_dao_memory_size{application="icefire-game",entityClass="ServerRecord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="ServerSeasonTaskProgress"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="SevenCaptureDailyInfo"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="SevenCaptureNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="ShieldRecord"} 12.0
game_dao_memory_size{application="icefire-game",entityClass="SiegeEnginesNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="SkinData"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="SoldierInfo"} 6.0
game_dao_memory_size{application="icefire-game",entityClass="StrongHoldNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="Trusteeship"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="TvtBattleServerDispatchRecord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="TvtPlayerSignupInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="TvtPlayerSimpleInfo"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="TvtQualifiedServer"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="UnActiveRoleMailRecord"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="WhispererBossNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="WhispererNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="WorkProgress"} 29.0
game_dao_memory_size{application="icefire-game",entityClass="WorldBossDongzhuoNode"} 0.0
game_dao_memory_size{application="icefire-game",entityClass="WorldBossNode"} 4407.0
game_dao_memory_size{application="icefire-game",entityClass="WorldCastleNode"} 0.0
# HELP game_packet_in_total game packet count
# TYPE game_packet_in_total counter
game_packet_in_total{application="icefire-game",type="1"} 1.0
game_packet_in_total{application="icefire-game",type="1100"} 1.0
game_packet_in_total{application="icefire-game",type="1130"} 1.0
game_packet_in_total{application="icefire-game",type="1300"} 1.0
game_packet_in_total{application="icefire-game",type="153"} 2.0
game_packet_in_total{application="icefire-game",type="155"} 6.0
game_packet_in_total{application="icefire-game",type="1600"} 1.0
game_packet_in_total{application="icefire-game",type="1753"} 2.0
game_packet_in_total{application="icefire-game",type="1951"} 1.0
game_packet_in_total{application="icefire-game",type="2"} 1.0
game_packet_in_total{application="icefire-game",type="2011"} 1.0
game_packet_in_total{application="icefire-game",type="2051"} 1.0
game_packet_in_total{application="icefire-game",type="2531"} 2.0
game_packet_in_total{application="icefire-game",type="3321"} 1.0
game_packet_in_total{application="icefire-game",type="3327"} 1.0
game_packet_in_total{application="icefire-game",type="3580"} 1.0
game_packet_in_total{application="icefire-game",type="4"} 19.0
game_packet_in_total{application="icefire-game",type="4461"} 1.0
game_packet_in_total{application="icefire-game",type="4463"} 1.0
game_packet_in_total{application="icefire-game",type="5151"} 1.0
game_packet_in_total{application="icefire-game",type="5155"} 1.0
game_packet_in_total{application="icefire-game",type="521"} 1.0
game_packet_in_total{application="icefire-game",type="6700"} 1.0
game_packet_in_total{application="icefire-game",type="6905"} 1.0
game_packet_in_total{application="icefire-game",type="7003"} 1.0
game_packet_in_total{application="icefire-game",type="714"} 1.0
game_packet_in_total{application="icefire-game",type="7242"} 1.0
game_packet_in_total{application="icefire-game",type="7270"} 1.0
game_packet_in_total{application="icefire-game",type="7387"} 2.0
game_packet_in_total{application="icefire-game",type="7458"} 1.0
game_packet_in_total{application="icefire-game",type="7565"} 1.0
game_packet_in_total{application="icefire-game",type="8121"} 1.0
# HELP game_packet_in_len game packet length
# TYPE game_packet_in_len summary
game_packet_in_len_count{application="icefire-game",type="1"} 1
game_packet_in_len_sum{application="icefire-game",type="1"} 260.0
game_packet_in_len_count{application="icefire-game",type="1100"} 1
game_packet_in_len_sum{application="icefire-game",type="1100"} 11.0
game_packet_in_len_count{application="icefire-game",type="1130"} 1
game_packet_in_len_sum{application="icefire-game",type="1130"} 13.0
game_packet_in_len_count{application="icefire-game",type="1300"} 1
game_packet_in_len_sum{application="icefire-game",type="1300"} 11.0
game_packet_in_len_count{application="icefire-game",type="153"} 2
game_packet_in_len_sum{application="icefire-game",type="153"} 28.0
game_packet_in_len_count{application="icefire-game",type="155"} 6
game_packet_in_len_sum{application="icefire-game",type="155"} 78.0
game_packet_in_len_count{application="icefire-game",type="1600"} 1
game_packet_in_len_sum{application="icefire-game",type="1600"} 13.0
game_packet_in_len_count{application="icefire-game",type="1753"} 2
game_packet_in_len_sum{application="icefire-game",type="1753"} 22.0
game_packet_in_len_count{application="icefire-game",type="1951"} 1
game_packet_in_len_sum{application="icefire-game",type="1951"} 11.0
game_packet_in_len_count{application="icefire-game",type="2"} 1
game_packet_in_len_sum{application="icefire-game",type="2"} 11.0
game_packet_in_len_count{application="icefire-game",type="2011"} 1
game_packet_in_len_sum{application="icefire-game",type="2011"} 15.0
game_packet_in_len_count{application="icefire-game",type="2051"} 1
game_packet_in_len_sum{application="icefire-game",type="2051"} 11.0
game_packet_in_len_count{application="icefire-game",type="2531"} 2
game_packet_in_len_sum{application="icefire-game",type="2531"} 22.0
game_packet_in_len_count{application="icefire-game",type="3321"} 1
game_packet_in_len_sum{application="icefire-game",type="3321"} 19.0
game_packet_in_len_count{application="icefire-game",type="3327"} 1
game_packet_in_len_sum{application="icefire-game",type="3327"} 12.0
game_packet_in_len_count{application="icefire-game",type="3580"} 1
game_packet_in_len_sum{application="icefire-game",type="3580"} 11.0
game_packet_in_len_count{application="icefire-game",type="4"} 19
game_packet_in_len_sum{application="icefire-game",type="4"} 456.0
game_packet_in_len_count{application="icefire-game",type="4461"} 1
game_packet_in_len_sum{application="icefire-game",type="4461"} 11.0
game_packet_in_len_count{application="icefire-game",type="4463"} 1
game_packet_in_len_sum{application="icefire-game",type="4463"} 11.0
game_packet_in_len_count{application="icefire-game",type="5151"} 1
game_packet_in_len_sum{application="icefire-game",type="5151"} 11.0
game_packet_in_len_count{application="icefire-game",type="5155"} 1
game_packet_in_len_sum{application="icefire-game",type="5155"} 11.0
game_packet_in_len_count{application="icefire-game",type="521"} 1
game_packet_in_len_sum{application="icefire-game",type="521"} 11.0
game_packet_in_len_count{application="icefire-game",type="6700"} 1
game_packet_in_len_sum{application="icefire-game",type="6700"} 11.0
game_packet_in_len_count{application="icefire-game",type="6905"} 1
game_packet_in_len_sum{application="icefire-game",type="6905"} 11.0
game_packet_in_len_count{application="icefire-game",type="7003"} 1
game_packet_in_len_sum{application="icefire-game",type="7003"} 11.0
game_packet_in_len_count{application="icefire-game",type="714"} 1
game_packet_in_len_sum{application="icefire-game",type="714"} 11.0
game_packet_in_len_count{application="icefire-game",type="7242"} 1
game_packet_in_len_sum{application="icefire-game",type="7242"} 11.0
game_packet_in_len_count{application="icefire-game",type="7270"} 1
game_packet_in_len_sum{application="icefire-game",type="7270"} 11.0
game_packet_in_len_count{application="icefire-game",type="7387"} 2
game_packet_in_len_sum{application="icefire-game",type="7387"} 22.0
game_packet_in_len_count{application="icefire-game",type="7458"} 1
game_packet_in_len_sum{application="icefire-game",type="7458"} 11.0
game_packet_in_len_count{application="icefire-game",type="7565"} 1
game_packet_in_len_sum{application="icefire-game",type="7565"} 11.0
game_packet_in_len_count{application="icefire-game",type="8121"} 1
game_packet_in_len_sum{application="icefire-game",type="8121"} 11.0
# HELP game_packet_in_len_max game packet length
# TYPE game_packet_in_len_max gauge
game_packet_in_len_max{application="icefire-game",type="1"} 260.0
game_packet_in_len_max{application="icefire-game",type="1100"} 11.0
game_packet_in_len_max{application="icefire-game",type="1130"} 13.0
game_packet_in_len_max{application="icefire-game",type="1300"} 11.0
game_packet_in_len_max{application="icefire-game",type="153"} 15.0
game_packet_in_len_max{application="icefire-game",type="155"} 13.0
game_packet_in_len_max{application="icefire-game",type="1600"} 13.0
game_packet_in_len_max{application="icefire-game",type="1753"} 11.0
game_packet_in_len_max{application="icefire-game",type="1951"} 11.0
game_packet_in_len_max{application="icefire-game",type="2"} 11.0
game_packet_in_len_max{application="icefire-game",type="2011"} 15.0
game_packet_in_len_max{application="icefire-game",type="2051"} 11.0
game_packet_in_len_max{application="icefire-game",type="2531"} 11.0
game_packet_in_len_max{application="icefire-game",type="3321"} 19.0
game_packet_in_len_max{application="icefire-game",type="3327"} 12.0
game_packet_in_len_max{application="icefire-game",type="3580"} 11.0
game_packet_in_len_max{application="icefire-game",type="4"} 24.0
game_packet_in_len_max{application="icefire-game",type="4461"} 11.0
game_packet_in_len_max{application="icefire-game",type="4463"} 11.0
game_packet_in_len_max{application="icefire-game",type="5151"} 11.0
game_packet_in_len_max{application="icefire-game",type="5155"} 11.0
game_packet_in_len_max{application="icefire-game",type="521"} 11.0
game_packet_in_len_max{application="icefire-game",type="6700"} 11.0
game_packet_in_len_max{application="icefire-game",type="6905"} 11.0
game_packet_in_len_max{application="icefire-game",type="7003"} 11.0
game_packet_in_len_max{application="icefire-game",type="714"} 11.0
game_packet_in_len_max{application="icefire-game",type="7242"} 11.0
game_packet_in_len_max{application="icefire-game",type="7270"} 11.0
game_packet_in_len_max{application="icefire-game",type="7387"} 11.0
game_packet_in_len_max{application="icefire-game",type="7458"} 11.0
game_packet_in_len_max{application="icefire-game",type="7565"} 11.0
game_packet_in_len_max{application="icefire-game",type="8121"} 11.0
# HELP game_packet_out_total game packet count
# TYPE game_packet_out_total counter
game_packet_out_total{application="icefire-game",type="10"} 1.0
game_packet_out_total{application="icefire-game",type="100"} 1.0
game_packet_out_total{application="icefire-game",type="101"} 1.0
game_packet_out_total{application="icefire-game",type="107"} 1.0
game_packet_out_total{application="icefire-game",type="1101"} 1.0
game_packet_out_total{application="icefire-game",type="1131"} 1.0
game_packet_out_total{application="icefire-game",type="119"} 1.0
game_packet_out_total{application="icefire-game",type="12"} 1.0
game_packet_out_total{application="icefire-game",type="1201"} 1.0
game_packet_out_total{application="icefire-game",type="1252"} 1.0
game_packet_out_total{application="icefire-game",type="1256"} 1.0
game_packet_out_total{application="icefire-game",type="1259"} 1.0
game_packet_out_total{application="icefire-game",type="1260"} 1.0
game_packet_out_total{application="icefire-game",type="1301"} 1.0
game_packet_out_total{application="icefire-game",type="1351"} 1.0
game_packet_out_total{application="icefire-game",type="1396"} 1.0
game_packet_out_total{application="icefire-game",type="145"} 1.0
game_packet_out_total{application="icefire-game",type="1461"} 1.0
game_packet_out_total{application="icefire-game",type="1469"} 1.0
game_packet_out_total{application="icefire-game",type="148"} 1.0
game_packet_out_total{application="icefire-game",type="154"} 3.0
game_packet_out_total{application="icefire-game",type="156"} 7.0
game_packet_out_total{application="icefire-game",type="160"} 1.0
game_packet_out_total{application="icefire-game",type="1610"} 2.0
game_packet_out_total{application="icefire-game",type="1627"} 2.0
game_packet_out_total{application="icefire-game",type="1651"} 1.0
game_packet_out_total{application="icefire-game",type="1691"} 1.0
game_packet_out_total{application="icefire-game",type="1754"} 2.0
game_packet_out_total{application="icefire-game",type="1952"} 2.0
game_packet_out_total{application="icefire-game",type="200"} 1.0
game_packet_out_total{application="icefire-game",type="2002"} 1.0
game_packet_out_total{application="icefire-game",type="2012"} 1.0
game_packet_out_total{application="icefire-game",type="2052"} 3.0
game_packet_out_total{application="icefire-game",type="2202"} 1.0
game_packet_out_total{application="icefire-game",type="226"} 1.0
game_packet_out_total{application="icefire-game",type="2532"} 3.0
game_packet_out_total{application="icefire-game",type="3328"} 1.0
game_packet_out_total{application="icefire-game",type="3331"} 2.0
game_packet_out_total{application="icefire-game",type="3382"} 1.0
game_packet_out_total{application="icefire-game",type="3474"} 1.0
game_packet_out_total{application="icefire-game",type="3475"} 1.0
game_packet_out_total{application="icefire-game",type="3522"} 1.0
game_packet_out_total{application="icefire-game",type="3581"} 2.0
game_packet_out_total{application="icefire-game",type="3601"} 1.0
game_packet_out_total{application="icefire-game",type="401"} 1.0
game_packet_out_total{application="icefire-game",type="4101"} 1.0
game_packet_out_total{application="icefire-game",type="4462"} 1.0
game_packet_out_total{application="icefire-game",type="4464"} 1.0
game_packet_out_total{application="icefire-game",type="4591"} 1.0
game_packet_out_total{application="icefire-game",type="4617"} 1.0
game_packet_out_total{application="icefire-game",type="4629"} 1.0
game_packet_out_total{application="icefire-game",type="4675"} 1.0
game_packet_out_total{application="icefire-game",type="4903"} 1.0
game_packet_out_total{application="icefire-game",type="4948"} 1.0
game_packet_out_total{application="icefire-game",type="4956"} 1.0
game_packet_out_total{application="icefire-game",type="5"} 19.0
game_packet_out_total{application="icefire-game",type="522"} 1.0
game_packet_out_total{application="icefire-game",type="534"} 1.0
game_packet_out_total{application="icefire-game",type="5671"} 1.0
game_packet_out_total{application="icefire-game",type="651"} 3.0
game_packet_out_total{application="icefire-game",type="6701"} 1.0
game_packet_out_total{application="icefire-game",type="6760"} 1.0
game_packet_out_total{application="icefire-game",type="6906"} 2.0
game_packet_out_total{application="icefire-game",type="6910"} 1.0
game_packet_out_total{application="icefire-game",type="6918"} 1.0
game_packet_out_total{application="icefire-game",type="6958"} 1.0
game_packet_out_total{application="icefire-game",type="6990"} 1.0
game_packet_out_total{application="icefire-game",type="7002"} 1.0
game_packet_out_total{application="icefire-game",type="701"} 1.0
game_packet_out_total{application="icefire-game",type="7102"} 1.0
game_packet_out_total{application="icefire-game",type="7112"} 1.0
game_packet_out_total{application="icefire-game",type="715"} 2.0
game_packet_out_total{application="icefire-game",type="7241"} 1.0
game_packet_out_total{application="icefire-game",type="7243"} 1.0
game_packet_out_total{application="icefire-game",type="7252"} 1.0
game_packet_out_total{application="icefire-game",type="7271"} 2.0
game_packet_out_total{application="icefire-game",type="7283"} 1.0
game_packet_out_total{application="icefire-game",type="7300"} 1.0
game_packet_out_total{application="icefire-game",type="7324"} 1.0
game_packet_out_total{application="icefire-game",type="7351"} 1.0
game_packet_out_total{application="icefire-game",type="7388"} 2.0
game_packet_out_total{application="icefire-game",type="7459"} 1.0
game_packet_out_total{application="icefire-game",type="7461"} 1.0
game_packet_out_total{application="icefire-game",type="7484"} 2.0
game_packet_out_total{application="icefire-game",type="7552"} 1.0
game_packet_out_total{application="icefire-game",type="7686"} 1.0
game_packet_out_total{application="icefire-game",type="7752"} 1.0
game_packet_out_total{application="icefire-game",type="7863"} 1.0
game_packet_out_total{application="icefire-game",type="8122"} 1.0
game_packet_out_total{application="icefire-game",type="855"} 1.0
game_packet_out_total{application="icefire-game",type="971"} 1.0
game_packet_out_total{application="icefire-game",type="976"} 1.0
# HELP game_packet_out_len game packet length
# TYPE game_packet_out_len summary
game_packet_out_len_count{application="icefire-game",type="10"} 1
game_packet_out_len_sum{application="icefire-game",type="10"} 11.0
game_packet_out_len_count{application="icefire-game",type="100"} 1
game_packet_out_len_sum{application="icefire-game",type="100"} 270.0
game_packet_out_len_count{application="icefire-game",type="101"} 1
game_packet_out_len_sum{application="icefire-game",type="101"} 47.0
game_packet_out_len_count{application="icefire-game",type="107"} 1
game_packet_out_len_sum{application="icefire-game",type="107"} 18.0
game_packet_out_len_count{application="icefire-game",type="1101"} 1
game_packet_out_len_sum{application="icefire-game",type="1101"} 11.0
game_packet_out_len_count{application="icefire-game",type="1131"} 1
game_packet_out_len_sum{application="icefire-game",type="1131"} 20.0
game_packet_out_len_count{application="icefire-game",type="119"} 1
game_packet_out_len_sum{application="icefire-game",type="119"} 23.0
game_packet_out_len_count{application="icefire-game",type="12"} 1
game_packet_out_len_sum{application="icefire-game",type="12"} 22.0
game_packet_out_len_count{application="icefire-game",type="1201"} 1
game_packet_out_len_sum{application="icefire-game",type="1201"} 13.0
game_packet_out_len_count{application="icefire-game",type="1252"} 1
game_packet_out_len_sum{application="icefire-game",type="1252"} 458.0
game_packet_out_len_count{application="icefire-game",type="1256"} 1
game_packet_out_len_sum{application="icefire-game",type="1256"} 47.0
game_packet_out_len_count{application="icefire-game",type="1259"} 1
game_packet_out_len_sum{application="icefire-game",type="1259"} 58.0
game_packet_out_len_count{application="icefire-game",type="1260"} 1
game_packet_out_len_sum{application="icefire-game",type="1260"} 13.0
game_packet_out_len_count{application="icefire-game",type="1301"} 1
game_packet_out_len_sum{application="icefire-game",type="1301"} 11.0
game_packet_out_len_count{application="icefire-game",type="1351"} 1
game_packet_out_len_sum{application="icefire-game",type="1351"} 3630.0
game_packet_out_len_count{application="icefire-game",type="1396"} 1
game_packet_out_len_sum{application="icefire-game",type="1396"} 24.0
game_packet_out_len_count{application="icefire-game",type="145"} 1
game_packet_out_len_sum{application="icefire-game",type="145"} 17.0
game_packet_out_len_count{application="icefire-game",type="1461"} 1
game_packet_out_len_sum{application="icefire-game",type="1461"} 13.0
game_packet_out_len_count{application="icefire-game",type="1469"} 1
game_packet_out_len_sum{application="icefire-game",type="1469"} 11.0
game_packet_out_len_count{application="icefire-game",type="148"} 1
game_packet_out_len_sum{application="icefire-game",type="148"} 200.0
game_packet_out_len_count{application="icefire-game",type="154"} 3
game_packet_out_len_sum{application="icefire-game",type="154"} 51.0
game_packet_out_len_count{application="icefire-game",type="156"} 7
game_packet_out_len_sum{application="icefire-game",type="156"} 245.0
game_packet_out_len_count{application="icefire-game",type="160"} 1
game_packet_out_len_sum{application="icefire-game",type="160"} 12.0
game_packet_out_len_count{application="icefire-game",type="1610"} 2
game_packet_out_len_sum{application="icefire-game",type="1610"} 238.0
game_packet_out_len_count{application="icefire-game",type="1627"} 2
game_packet_out_len_sum{application="icefire-game",type="1627"} 26.0
game_packet_out_len_count{application="icefire-game",type="1651"} 1
game_packet_out_len_sum{application="icefire-game",type="1651"} 40.0
game_packet_out_len_count{application="icefire-game",type="1691"} 1
game_packet_out_len_sum{application="icefire-game",type="1691"} 15.0
game_packet_out_len_count{application="icefire-game",type="1754"} 2
game_packet_out_len_sum{application="icefire-game",type="1754"} 24.0
game_packet_out_len_count{application="icefire-game",type="1952"} 2
game_packet_out_len_sum{application="icefire-game",type="1952"} 432.0
game_packet_out_len_count{application="icefire-game",type="200"} 1
game_packet_out_len_sum{application="icefire-game",type="200"} 1216.0
game_packet_out_len_count{application="icefire-game",type="2002"} 1
game_packet_out_len_sum{application="icefire-game",type="2002"} 12.0
game_packet_out_len_count{application="icefire-game",type="2012"} 1
game_packet_out_len_sum{application="icefire-game",type="2012"} 167.0
game_packet_out_len_count{application="icefire-game",type="2052"} 3
game_packet_out_len_sum{application="icefire-game",type="2052"} 105.0
game_packet_out_len_count{application="icefire-game",type="2202"} 1
game_packet_out_len_sum{application="icefire-game",type="2202"} 130.0
game_packet_out_len_count{application="icefire-game",type="226"} 1
game_packet_out_len_sum{application="icefire-game",type="226"} 25.0
game_packet_out_len_count{application="icefire-game",type="2532"} 3
game_packet_out_len_sum{application="icefire-game",type="2532"} 57.0
game_packet_out_len_count{application="icefire-game",type="3328"} 1
game_packet_out_len_sum{application="icefire-game",type="3328"} 12.0
game_packet_out_len_count{application="icefire-game",type="3331"} 2
game_packet_out_len_sum{application="icefire-game",type="3331"} 26.0
game_packet_out_len_count{application="icefire-game",type="3382"} 1
game_packet_out_len_sum{application="icefire-game",type="3382"} 12.0
game_packet_out_len_count{application="icefire-game",type="3474"} 1
game_packet_out_len_sum{application="icefire-game",type="3474"} 13.0
game_packet_out_len_count{application="icefire-game",type="3475"} 1
game_packet_out_len_sum{application="icefire-game",type="3475"} 45.0
game_packet_out_len_count{application="icefire-game",type="3522"} 1
game_packet_out_len_sum{application="icefire-game",type="3522"} 85.0
game_packet_out_len_count{application="icefire-game",type="3581"} 2
game_packet_out_len_sum{application="icefire-game",type="3581"} 1430.0
game_packet_out_len_count{application="icefire-game",type="3601"} 1
game_packet_out_len_sum{application="icefire-game",type="3601"} 265.0
game_packet_out_len_count{application="icefire-game",type="401"} 1
game_packet_out_len_sum{application="icefire-game",type="401"} 58.0
game_packet_out_len_count{application="icefire-game",type="4101"} 1
game_packet_out_len_sum{application="icefire-game",type="4101"} 13.0
game_packet_out_len_count{application="icefire-game",type="4462"} 1
game_packet_out_len_sum{application="icefire-game",type="4462"} 63.0
game_packet_out_len_count{application="icefire-game",type="4464"} 1
game_packet_out_len_sum{application="icefire-game",type="4464"} 13.0
game_packet_out_len_count{application="icefire-game",type="4591"} 1
game_packet_out_len_sum{application="icefire-game",type="4591"} 11.0
game_packet_out_len_count{application="icefire-game",type="4617"} 1
game_packet_out_len_sum{application="icefire-game",type="4617"} 23.0
game_packet_out_len_count{application="icefire-game",type="4629"} 1
game_packet_out_len_sum{application="icefire-game",type="4629"} 17.0
game_packet_out_len_count{application="icefire-game",type="4675"} 1
game_packet_out_len_sum{application="icefire-game",type="4675"} 23.0
game_packet_out_len_count{application="icefire-game",type="4903"} 1
game_packet_out_len_sum{application="icefire-game",type="4903"} 38.0
game_packet_out_len_count{application="icefire-game",type="4948"} 1
game_packet_out_len_sum{application="icefire-game",type="4948"} 17.0
game_packet_out_len_count{application="icefire-game",type="4956"} 1
game_packet_out_len_sum{application="icefire-game",type="4956"} 15.0
game_packet_out_len_count{application="icefire-game",type="5"} 19
game_packet_out_len_sum{application="icefire-game",type="5"} 494.0
game_packet_out_len_count{application="icefire-game",type="522"} 1
game_packet_out_len_sum{application="icefire-game",type="522"} 78.0
game_packet_out_len_count{application="icefire-game",type="534"} 1
game_packet_out_len_sum{application="icefire-game",type="534"} 13.0
game_packet_out_len_count{application="icefire-game",type="5671"} 1
game_packet_out_len_sum{application="icefire-game",type="5671"} 260.0
game_packet_out_len_count{application="icefire-game",type="651"} 3
game_packet_out_len_sum{application="icefire-game",type="651"} 48.0
game_packet_out_len_count{application="icefire-game",type="6701"} 1
game_packet_out_len_sum{application="icefire-game",type="6701"} 665.0
game_packet_out_len_count{application="icefire-game",type="6760"} 1
game_packet_out_len_sum{application="icefire-game",type="6760"} 22.0
game_packet_out_len_count{application="icefire-game",type="6906"} 2
game_packet_out_len_sum{application="icefire-game",type="6906"} 22.0
game_packet_out_len_count{application="icefire-game",type="6910"} 1
game_packet_out_len_sum{application="icefire-game",type="6910"} 3362.0
game_packet_out_len_count{application="icefire-game",type="6918"} 1
game_packet_out_len_sum{application="icefire-game",type="6918"} 13.0
game_packet_out_len_count{application="icefire-game",type="6958"} 1
game_packet_out_len_sum{application="icefire-game",type="6958"} 181.0
game_packet_out_len_count{application="icefire-game",type="6990"} 1
game_packet_out_len_sum{application="icefire-game",type="6990"} 759.0
game_packet_out_len_count{application="icefire-game",type="7002"} 1
game_packet_out_len_sum{application="icefire-game",type="7002"} 18.0
game_packet_out_len_count{application="icefire-game",type="701"} 1
game_packet_out_len_sum{application="icefire-game",type="701"} 336.0
game_packet_out_len_count{application="icefire-game",type="7102"} 1
game_packet_out_len_sum{application="icefire-game",type="7102"} 23.0
game_packet_out_len_count{application="icefire-game",type="7112"} 1
game_packet_out_len_sum{application="icefire-game",type="7112"} 104.0
game_packet_out_len_count{application="icefire-game",type="715"} 2
game_packet_out_len_sum{application="icefire-game",type="715"} 128.0
game_packet_out_len_count{application="icefire-game",type="7241"} 1
game_packet_out_len_sum{application="icefire-game",type="7241"} 13.0
game_packet_out_len_count{application="icefire-game",type="7243"} 1
game_packet_out_len_sum{application="icefire-game",type="7243"} 13.0
game_packet_out_len_count{application="icefire-game",type="7252"} 1
game_packet_out_len_sum{application="icefire-game",type="7252"} 318.0
game_packet_out_len_count{application="icefire-game",type="7271"} 2
game_packet_out_len_sum{application="icefire-game",type="7271"} 30.0
game_packet_out_len_count{application="icefire-game",type="7283"} 1
game_packet_out_len_sum{application="icefire-game",type="7283"} 16.0
game_packet_out_len_count{application="icefire-game",type="7300"} 1
game_packet_out_len_sum{application="icefire-game",type="7300"} 24.0
game_packet_out_len_count{application="icefire-game",type="7324"} 1
game_packet_out_len_sum{application="icefire-game",type="7324"} 94.0
game_packet_out_len_count{application="icefire-game",type="7351"} 1
game_packet_out_len_sum{application="icefire-game",type="7351"} 313.0
game_packet_out_len_count{application="icefire-game",type="7388"} 2
game_packet_out_len_sum{application="icefire-game",type="7388"} 492.0
game_packet_out_len_count{application="icefire-game",type="7459"} 1
game_packet_out_len_sum{application="icefire-game",type="7459"} 13.0
game_packet_out_len_count{application="icefire-game",type="7461"} 1
game_packet_out_len_sum{application="icefire-game",type="7461"} 13.0
game_packet_out_len_count{application="icefire-game",type="7484"} 2
game_packet_out_len_sum{application="icefire-game",type="7484"} 26.0
game_packet_out_len_count{application="icefire-game",type="7552"} 1
game_packet_out_len_sum{application="icefire-game",type="7552"} 13.0
game_packet_out_len_count{application="icefire-game",type="7686"} 1
game_packet_out_len_sum{application="icefire-game",type="7686"} 13.0
game_packet_out_len_count{application="icefire-game",type="7752"} 1
game_packet_out_len_sum{application="icefire-game",type="7752"} 15.0
game_packet_out_len_count{application="icefire-game",type="7863"} 1
game_packet_out_len_sum{application="icefire-game",type="7863"} 25.0
game_packet_out_len_count{application="icefire-game",type="8122"} 1
game_packet_out_len_sum{application="icefire-game",type="8122"} 26.0
game_packet_out_len_count{application="icefire-game",type="855"} 1
game_packet_out_len_sum{application="icefire-game",type="855"} 11.0
game_packet_out_len_count{application="icefire-game",type="971"} 1
game_packet_out_len_sum{application="icefire-game",type="971"} 65.0
game_packet_out_len_count{application="icefire-game",type="976"} 1
game_packet_out_len_sum{application="icefire-game",type="976"} 21.0
# HELP game_packet_out_len_max game packet length
# TYPE game_packet_out_len_max gauge
game_packet_out_len_max{application="icefire-game",type="10"} 11.0
game_packet_out_len_max{application="icefire-game",type="100"} 270.0
game_packet_out_len_max{application="icefire-game",type="101"} 47.0
game_packet_out_len_max{application="icefire-game",type="107"} 18.0
game_packet_out_len_max{application="icefire-game",type="1101"} 11.0
game_packet_out_len_max{application="icefire-game",type="1131"} 20.0
game_packet_out_len_max{application="icefire-game",type="119"} 23.0
game_packet_out_len_max{application="icefire-game",type="12"} 22.0
game_packet_out_len_max{application="icefire-game",type="1201"} 13.0
game_packet_out_len_max{application="icefire-game",type="1252"} 458.0
game_packet_out_len_max{application="icefire-game",type="1256"} 47.0
game_packet_out_len_max{application="icefire-game",type="1259"} 58.0
game_packet_out_len_max{application="icefire-game",type="1260"} 13.0
game_packet_out_len_max{application="icefire-game",type="1301"} 11.0
game_packet_out_len_max{application="icefire-game",type="1351"} 3630.0
game_packet_out_len_max{application="icefire-game",type="1396"} 24.0
game_packet_out_len_max{application="icefire-game",type="145"} 17.0
game_packet_out_len_max{application="icefire-game",type="1461"} 13.0
game_packet_out_len_max{application="icefire-game",type="1469"} 11.0
game_packet_out_len_max{application="icefire-game",type="148"} 200.0
game_packet_out_len_max{application="icefire-game",type="154"} 17.0
game_packet_out_len_max{application="icefire-game",type="156"} 35.0
game_packet_out_len_max{application="icefire-game",type="160"} 12.0
game_packet_out_len_max{application="icefire-game",type="1610"} 119.0
game_packet_out_len_max{application="icefire-game",type="1627"} 13.0
game_packet_out_len_max{application="icefire-game",type="1651"} 40.0
game_packet_out_len_max{application="icefire-game",type="1691"} 15.0
game_packet_out_len_max{application="icefire-game",type="1754"} 12.0
game_packet_out_len_max{application="icefire-game",type="1952"} 216.0
game_packet_out_len_max{application="icefire-game",type="200"} 1216.0
game_packet_out_len_max{application="icefire-game",type="2002"} 12.0
game_packet_out_len_max{application="icefire-game",type="2012"} 167.0
game_packet_out_len_max{application="icefire-game",type="2052"} 35.0
game_packet_out_len_max{application="icefire-game",type="2202"} 130.0
game_packet_out_len_max{application="icefire-game",type="226"} 25.0
game_packet_out_len_max{application="icefire-game",type="2532"} 19.0
game_packet_out_len_max{application="icefire-game",type="3328"} 12.0
game_packet_out_len_max{application="icefire-game",type="3331"} 13.0
game_packet_out_len_max{application="icefire-game",type="3382"} 12.0
game_packet_out_len_max{application="icefire-game",type="3474"} 13.0
game_packet_out_len_max{application="icefire-game",type="3475"} 45.0
game_packet_out_len_max{application="icefire-game",type="3522"} 85.0
game_packet_out_len_max{application="icefire-game",type="3581"} 715.0
game_packet_out_len_max{application="icefire-game",type="3601"} 265.0
game_packet_out_len_max{application="icefire-game",type="401"} 58.0
game_packet_out_len_max{application="icefire-game",type="4101"} 13.0
game_packet_out_len_max{application="icefire-game",type="4462"} 63.0
game_packet_out_len_max{application="icefire-game",type="4464"} 13.0
game_packet_out_len_max{application="icefire-game",type="4591"} 11.0
game_packet_out_len_max{application="icefire-game",type="4617"} 23.0
game_packet_out_len_max{application="icefire-game",type="4629"} 17.0
game_packet_out_len_max{application="icefire-game",type="4675"} 23.0
game_packet_out_len_max{application="icefire-game",type="4903"} 38.0
game_packet_out_len_max{application="icefire-game",type="4948"} 17.0
game_packet_out_len_max{application="icefire-game",type="4956"} 15.0
game_packet_out_len_max{application="icefire-game",type="5"} 26.0
game_packet_out_len_max{application="icefire-game",type="522"} 78.0
game_packet_out_len_max{application="icefire-game",type="534"} 13.0
game_packet_out_len_max{application="icefire-game",type="5671"} 260.0
game_packet_out_len_max{application="icefire-game",type="651"} 16.0
game_packet_out_len_max{application="icefire-game",type="6701"} 665.0
game_packet_out_len_max{application="icefire-game",type="6760"} 22.0
game_packet_out_len_max{application="icefire-game",type="6906"} 11.0
game_packet_out_len_max{application="icefire-game",type="6910"} 3362.0
game_packet_out_len_max{application="icefire-game",type="6918"} 13.0
game_packet_out_len_max{application="icefire-game",type="6958"} 181.0
game_packet_out_len_max{application="icefire-game",type="6990"} 759.0
game_packet_out_len_max{application="icefire-game",type="7002"} 18.0
game_packet_out_len_max{application="icefire-game",type="701"} 336.0
game_packet_out_len_max{application="icefire-game",type="7102"} 23.0
game_packet_out_len_max{application="icefire-game",type="7112"} 104.0
game_packet_out_len_max{application="icefire-game",type="715"} 64.0
game_packet_out_len_max{application="icefire-game",type="7241"} 13.0
game_packet_out_len_max{application="icefire-game",type="7243"} 13.0
game_packet_out_len_max{application="icefire-game",type="7252"} 318.0
game_packet_out_len_max{application="icefire-game",type="7271"} 15.0
game_packet_out_len_max{application="icefire-game",type="7283"} 16.0
game_packet_out_len_max{application="icefire-game",type="7300"} 24.0
game_packet_out_len_max{application="icefire-game",type="7324"} 94.0
game_packet_out_len_max{application="icefire-game",type="7351"} 313.0
game_packet_out_len_max{application="icefire-game",type="7388"} 246.0
game_packet_out_len_max{application="icefire-game",type="7459"} 13.0
game_packet_out_len_max{application="icefire-game",type="7461"} 13.0
game_packet_out_len_max{application="icefire-game",type="7484"} 13.0
game_packet_out_len_max{application="icefire-game",type="7552"} 13.0
game_packet_out_len_max{application="icefire-game",type="7686"} 13.0
game_packet_out_len_max{application="icefire-game",type="7752"} 15.0
game_packet_out_len_max{application="icefire-game",type="7863"} 25.0
game_packet_out_len_max{application="icefire-game",type="8122"} 26.0
game_packet_out_len_max{application="icefire-game",type="855"} 11.0
game_packet_out_len_max{application="icefire-game",type="971"} 65.0
game_packet_out_len_max{application="icefire-game",type="976"} 21.0
# TYPE http_client_requests_total counter
http_client_requests_total{application="icefire-game",client_name="HttpUtil",method="POST",outcome="SUCCESS",status="200"} 2.0
# HELP http_client_requests_seconds Apache HttpClient 请求计时
# TYPE http_client_requests_seconds summary
http_client_requests_seconds_count{application="icefire-game",client_name="HttpUtil",method="POST",outcome="SUCCESS",status="200",uri="/cgi-bin/stable_token"} 2
http_client_requests_seconds_sum{application="icefire-game",client_name="HttpUtil",method="POST",outcome="SUCCESS",status="200",uri="/cgi-bin/stable_token"} 0.626
# HELP http_client_requests_seconds_max Apache HttpClient 请求计时
# TYPE http_client_requests_seconds_max gauge
http_client_requests_seconds_max{application="icefire-game",client_name="HttpUtil",method="POST",outcome="SUCCESS",status="200",uri="/cgi-bin/stable_token"} 0.0
# HELP jvm_buffer_count_buffers An estimate of the number of buffers in the pool
# TYPE jvm_buffer_count_buffers gauge
jvm_buffer_count_buffers{application="icefire-game",id="direct"} 38.0
jvm_buffer_count_buffers{application="icefire-game",id="mapped"} 0.0
jvm_buffer_count_buffers{application="icefire-game",id="mapped - 'non-volatile memory'"} 0.0
# HELP jvm_buffer_memory_used_bytes An estimate of the memory that the Java virtual machine is using for this buffer pool
# TYPE jvm_buffer_memory_used_bytes gauge
jvm_buffer_memory_used_bytes{application="icefire-game",id="direct"} 3.4457533E7
jvm_buffer_memory_used_bytes{application="icefire-game",id="mapped"} 0.0
jvm_buffer_memory_used_bytes{application="icefire-game",id="mapped - 'non-volatile memory'"} 0.0
# HELP jvm_buffer_total_capacity_bytes An estimate of the total capacity of the buffers in this pool
# TYPE jvm_buffer_total_capacity_bytes gauge
jvm_buffer_total_capacity_bytes{application="icefire-game",id="direct"} 3.4457532E7
jvm_buffer_total_capacity_bytes{application="icefire-game",id="mapped"} 0.0
jvm_buffer_total_capacity_bytes{application="icefire-game",id="mapped - 'non-volatile memory'"} 0.0
# HELP jvm_classes_loaded_classes The number of classes that are currently loaded in the Java virtual machine
# TYPE jvm_classes_loaded_classes gauge
jvm_classes_loaded_classes{application="icefire-game"} 25961.0
# HELP jvm_classes_unloaded_classes_total The number of classes unloaded in the Java virtual machine
# TYPE jvm_classes_unloaded_classes_total counter
jvm_classes_unloaded_classes_total{application="icefire-game"} 3.0
# HELP jvm_compilation_time_ms_total The approximate accumulated elapsed time spent in compilation
# TYPE jvm_compilation_time_ms_total counter
jvm_compilation_time_ms_total{application="icefire-game",compiler="HotSpot 64-Bit Tiered Compilers"} 33708.0
# HELP jvm_gc_concurrent_phase_time_seconds Time spent in concurrent phase
# TYPE jvm_gc_concurrent_phase_time_seconds summary
jvm_gc_concurrent_phase_time_seconds_count{action="end of concurrent GC pause",application="icefire-game",cause="No GC",gc="G1 Concurrent GC"} 10
jvm_gc_concurrent_phase_time_seconds_sum{action="end of concurrent GC pause",application="icefire-game",cause="No GC",gc="G1 Concurrent GC"} 0.019
# HELP jvm_gc_concurrent_phase_time_seconds_max Time spent in concurrent phase
# TYPE jvm_gc_concurrent_phase_time_seconds_max gauge
jvm_gc_concurrent_phase_time_seconds_max{action="end of concurrent GC pause",application="icefire-game",cause="No GC",gc="G1 Concurrent GC"} 0.0
# HELP jvm_gc_live_data_size_bytes Size of long-lived heap memory pool after reclamation
# TYPE jvm_gc_live_data_size_bytes gauge
jvm_gc_live_data_size_bytes{application="icefire-game"} 5.20189736E8
# HELP jvm_gc_max_data_size_bytes Max size of long-lived heap memory pool
# TYPE jvm_gc_max_data_size_bytes gauge
jvm_gc_max_data_size_bytes{application="icefire-game"} 8.518631424E9
# HELP jvm_gc_memory_allocated_bytes_total Incremented for an increase in the size of the (young) heap memory pool after one GC to before the next
# TYPE jvm_gc_memory_allocated_bytes_total counter
jvm_gc_memory_allocated_bytes_total{application="icefire-game"} 3.430940672E9
# HELP jvm_gc_memory_promoted_bytes_total Count of positive increases in the size of the old generation memory pool before GC to after GC
# TYPE jvm_gc_memory_promoted_bytes_total counter
jvm_gc_memory_promoted_bytes_total{application="icefire-game"} 1.91804888E8
# HELP jvm_gc_pause_seconds Time spent in GC pause
# TYPE jvm_gc_pause_seconds summary
jvm_gc_pause_seconds_count{action="end of minor GC",application="icefire-game",cause="G1 Evacuation Pause",gc="G1 Young Generation"} 13
jvm_gc_pause_seconds_sum{action="end of minor GC",application="icefire-game",cause="G1 Evacuation Pause",gc="G1 Young Generation"} 0.154
jvm_gc_pause_seconds_count{action="end of minor GC",application="icefire-game",cause="G1 Humongous Allocation",gc="G1 Young Generation"} 5
jvm_gc_pause_seconds_sum{action="end of minor GC",application="icefire-game",cause="G1 Humongous Allocation",gc="G1 Young Generation"} 0.039
# HELP jvm_gc_pause_seconds_max Time spent in GC pause
# TYPE jvm_gc_pause_seconds_max gauge
jvm_gc_pause_seconds_max{action="end of minor GC",application="icefire-game",cause="G1 Evacuation Pause",gc="G1 Young Generation"} 0.0
jvm_gc_pause_seconds_max{action="end of minor GC",application="icefire-game",cause="G1 Humongous Allocation",gc="G1 Young Generation"} 0.0
# HELP jvm_memory_committed_bytes The amount of memory in bytes that is committed for the Java virtual machine to use
# TYPE jvm_memory_committed_bytes gauge
jvm_memory_committed_bytes{application="icefire-game",area="heap",id="G1 Eden Space"} 7.7594624E8
jvm_memory_committed_bytes{application="icefire-game",area="heap",id="G1 Old Gen"} 7.80140544E8
jvm_memory_committed_bytes{application="icefire-game",area="heap",id="G1 Survivor Space"} 1.2582912E7
jvm_memory_committed_bytes{application="icefire-game",area="nonheap",id="CodeHeap 'non-nmethods'"} 4653056.0
jvm_memory_committed_bytes{application="icefire-game",area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 1.6973824E7
jvm_memory_committed_bytes{application="icefire-game",area="nonheap",id="CodeHeap 'profiled nmethods'"} 3.342336E7
jvm_memory_committed_bytes{application="icefire-game",area="nonheap",id="Compressed Class Space"} 1.7956864E7
jvm_memory_committed_bytes{application="icefire-game",area="nonheap",id="Metaspace"} 1.51519232E8
# HELP jvm_memory_heap_usage_ratio 堆内存使用率
# TYPE jvm_memory_heap_usage_ratio gauge
jvm_memory_heap_usage_ratio{application="icefire-game",type="heap"} 0.1539093319974117
# HELP jvm_memory_max_bytes The maximum amount of memory in bytes that can be used for memory management
# TYPE jvm_memory_max_bytes gauge
jvm_memory_max_bytes{application="icefire-game",area="heap",id="G1 Eden Space"} -1.0
jvm_memory_max_bytes{application="icefire-game",area="heap",id="G1 Old Gen"} 8.518631424E9
jvm_memory_max_bytes{application="icefire-game",area="heap",id="G1 Survivor Space"} -1.0
jvm_memory_max_bytes{application="icefire-game",area="nonheap",id="CodeHeap 'non-nmethods'"} 7667712.0
jvm_memory_max_bytes{application="icefire-game",area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 1.22028032E8
jvm_memory_max_bytes{application="icefire-game",area="nonheap",id="CodeHeap 'profiled nmethods'"} 1.21962496E8
jvm_memory_max_bytes{application="icefire-game",area="nonheap",id="Compressed Class Space"} 1.073741824E9
jvm_memory_max_bytes{application="icefire-game",area="nonheap",id="Metaspace"} -1.0
# HELP jvm_memory_used_bytes The amount of used memory
# TYPE jvm_memory_used_bytes gauge
jvm_memory_used_bytes{application="icefire-game",area="heap",id="G1 Eden Space"} 7.25614592E8
jvm_memory_used_bytes{application="icefire-game",area="heap",id="G1 Old Gen"} 5.7083496E8
jvm_memory_used_bytes{application="icefire-game",area="heap",id="G1 Survivor Space"} 9726080.0
jvm_memory_used_bytes{application="icefire-game",area="nonheap",id="CodeHeap 'non-nmethods'"} 2800128.0
jvm_memory_used_bytes{application="icefire-game",area="nonheap",id="CodeHeap 'non-profiled nmethods'"} 1.6920832E7
jvm_memory_used_bytes{application="icefire-game",area="nonheap",id="CodeHeap 'profiled nmethods'"} 3.3392768E7
jvm_memory_used_bytes{application="icefire-game",area="nonheap",id="Compressed Class Space"} 1.7131176E7
jvm_memory_used_bytes{application="icefire-game",area="nonheap",id="Metaspace"} 1.49916968E8
# HELP jvm_threads_active 活跃线程数
# TYPE jvm_threads_active gauge
jvm_threads_active{application="icefire-game",type="active"} 100.0
# HELP jvm_threads_daemon_threads The current number of live daemon threads
# TYPE jvm_threads_daemon_threads gauge
jvm_threads_daemon_threads{application="icefire-game"} 73.0
# HELP jvm_threads_live_threads The current number of live threads including both daemon and non-daemon threads
# TYPE jvm_threads_live_threads gauge
jvm_threads_live_threads{application="icefire-game"} 122.0
# HELP jvm_threads_peak_threads The peak live thread count since the Java virtual machine started or peak was reset
# TYPE jvm_threads_peak_threads gauge
jvm_threads_peak_threads{application="icefire-game"} 123.0
# HELP jvm_threads_started_threads_total The total number of application threads started in the JVM
# TYPE jvm_threads_started_threads_total counter
jvm_threads_started_threads_total{application="icefire-game"} 185.0
# HELP jvm_threads_states_threads The current number of threads
# TYPE jvm_threads_states_threads gauge
jvm_threads_states_threads{application="icefire-game",state="blocked"} 0.0
jvm_threads_states_threads{application="icefire-game",state="new"} 0.0
jvm_threads_states_threads{application="icefire-game",state="runnable"} 24.0
jvm_threads_states_threads{application="icefire-game",state="terminated"} 0.0
jvm_threads_states_threads{application="icefire-game",state="timed-waiting"} 23.0
jvm_threads_states_threads{application="icefire-game",state="waiting"} 75.0
# HELP mongodb_command_total MongoDB command execution count
# TYPE mongodb_command_total counter
mongodb_command_total{application="icefire-game",collection="abstractemail",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="abstractemail",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="abstractemail",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="achieveinforecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="achieveinforecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="achieveinforecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="achievetaskrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="achievetaskrecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="achievetaskrecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="achievetaskrecord",command="getMore",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="achievetaskrecord",command="getMore",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="achievetaskrecord",command="getMore",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="activity",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="activity",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="activity",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="activity",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="activity",command="update",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="activity",command="update",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="activityrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="activityrecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="activityrecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="activityturn",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="activityturn",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="activityturn",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliance",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliance",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliance",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliance",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliance",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliance",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="allianceaffair",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="allianceaffair",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="allianceaffair",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebattleinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancebattleinfo",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebattleinfo",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebattleplayerinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancebattleplayerinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="alliancebattleplayerinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="alliancebossnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancebossnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebossnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebossroledonateinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancebossroledonateinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="alliancebossroledonateinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="alliancebuildingbase",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancebuildingbase",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebuildingbase",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebuildingnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancebuildingnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebuildingnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebuildingtech",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancebuildingtech",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancebuildingtech",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancecommonactivityinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancecommonactivityinfo",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancecommonactivityinfo",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancecommonranksnap",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancecommonranksnap",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancecommonranksnap",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancefeasthallnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancefeasthallnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancefeasthallnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancefeastnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancefeastnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancefeastnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancegift",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancegift",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="alliancegift",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="allianceleadermission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="allianceleadermission",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="allianceleadermission",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancelog",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancelog",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancelog",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancemark",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancemark",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancemark",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancemember",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancemember",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancemember",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancemessagebord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancemessagebord",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancemessagebord",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancemissionbase",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancemissionbase",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancemissionbase",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancemissionmemberinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancemissionmemberinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="alliancemissionmemberinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="allianceoprecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="allianceoprecord",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="allianceoprecord",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancereplace",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancereplace",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancereplace",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancerequest",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancerequest",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancerequest",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancesetting",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancesetting",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancesetting",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancetech",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancetech",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancetech",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancewar",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancewar",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancewar",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancewhispereractivityrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="alliancewhispereractivityrecord",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="alliancewhispereractivityrecord",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="armyinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="armyinfo",command="find",database="gameserver_1",result="success"} 3.0
mongodb_command_total{application="icefire-game",collection="armyinfo",command="find",database="gameserver_1",result="total"} 3.0
mongodb_command_total{application="icefire-game",collection="armypreprogrammed",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="armypreprogrammed",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="armypreprogrammed",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="armypresetinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="armypresetinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="armypresetinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="barbariancitynode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="barbariancitynode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="barbariancitynode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="bingoactivitycontext",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="bingoactivitycontext",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="bingoactivitycontext",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="buff",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="buff",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="buff",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="caravan",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="caravan",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="caravan",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="caravantrade",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="caravantrade",command="find",database="gameserver_1",result="success"} 3.0
mongodb_command_total{application="icefire-game",collection="caravantrade",command="find",database="gameserver_1",result="total"} 3.0
mongodb_command_total{application="icefire-game",collection="citybuild",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="citybuild",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="citybuild",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="citybuild",command="getMore",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="citybuild",command="getMore",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="citybuild",command="getMore",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="commonmissioninfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="commonmissioninfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="commonmissioninfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="csaactivityhistory",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="csaactivityhistory",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="csaactivityhistory",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="csagameserverresult",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="csagameserverresult",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="csagameserverresult",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="csaserverbattleinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="csaserverbattleinfo",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="csaserverbattleinfo",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="csaservertrophy",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="csaservertrophy",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="csaservertrophy",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="dongzhuobossbox",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="dongzhuobossbox",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="dongzhuobossbox",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgadmissionpush",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="gvgadmissionpush",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgadmissionpush",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgalliancebattlesummary",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="gvgalliancebattlesummary",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgalliancebattlesummary",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgcupapplyinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="gvgcupapplyinfo",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgcupapplyinfo",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgqualifiedalliance",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="gvgqualifiedalliance",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgqualifiedalliance",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgrolearenainfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="gvgrolearenainfo",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgrolearenainfo",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="gvgroleinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="gvgroleinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="gvgroleinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="gvgrolewillstatusinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="gvgrolewillstatusinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="gvgrolewillstatusinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="hero",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="hero",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="hero",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="horsemating",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="horsemating",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="horsemating",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="horsemating",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="horsemating",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="horsemating",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="icerolewheelinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="icerolewheelinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="icerolewheelinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="id_1",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="id_1",command="update",database="gameserver_1",result="success"} 3.0
mongodb_command_total{application="icefire-game",collection="id_1",command="update",database="gameserver_1",result="total"} 3.0
mongodb_command_total{application="icefire-game",collection="importreward",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="importreward",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="importreward",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="integratedspringboard",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="integratedspringboard",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="integratedspringboard",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="item",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="item",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="item",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="item",command="getMore",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="item",command="getMore",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="item",command="getMore",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="itemguarantee",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="itemguarantee",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="itemguarantee",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="itemrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="itemrecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="itemrecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="kingdombuff",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="kingdombuff",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="kingdombuff",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="kvkhistoryallianceprosperity",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="kvkhistoryallianceprosperity",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="kvkhistoryallianceprosperity",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="kvkhistorylegionprosperity",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="kvkhistorylegionprosperity",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="kvkhistorylegionprosperity",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="legionmilestone",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="legionmilestone",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="legionmilestone",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="loginrewardrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="loginrewardrecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="loginrewardrecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="milestone",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="milestone",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="milestone",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="missionbossnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="missionbossnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="missionbossnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="newplayermission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="newplayermission",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="newplayermission",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="newplayermission",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="newplayermission",command="update",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="newplayermission",command="update",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="delete",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="delete",database="gameserver_1",result="success"} 140.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="delete",database="gameserver_1",result="total"} 140.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="getMore",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="getMore",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="getMore",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="update",database="gameserver_1",result="success"} 64.0
mongodb_command_total{application="icefire-game",collection="newresnode",command="update",database="gameserver_1",result="total"} 64.0
mongodb_command_total{application="icefire-game",collection="newstrongestlordsplan",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="newstrongestlordsplan",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="newstrongestlordsplan",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="newstrongestplayerinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="newstrongestplayerinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="newstrongestplayerinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="npccenternode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="npccenternode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="npccenternode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="delete",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="delete",database="gameserver_1",result="success"} 10.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="delete",database="gameserver_1",result="total"} 10.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="getMore",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="getMore",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="getMore",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="update",database="gameserver_1",result="success"} 8.0
mongodb_command_total{application="icefire-game",collection="npcnode",command="update",database="gameserver_1",result="total"} 8.0
mongodb_command_total{application="icefire-game",collection="officialsinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="officialsinfo",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="officialsinfo",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="officialsinfo",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="officialsinfo",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="officialsinfo",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="onlinerewarddata",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="onlinerewarddata",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="onlinerewarddata",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="onlinerewarddata",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="onlinerewarddata",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="onlinerewarddata",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="personalmilestone",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="personalmilestone",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="personalmilestone",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playerchaptermission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerchaptermission",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playerchaptermission",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playercommonactivityinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playercommonactivityinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playercommonactivityinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playercommonranksnap",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playercommonranksnap",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="playercommonranksnap",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="playerdailymission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerdailymission",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playerdailymission",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playerfeedermission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerfeedermission",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playerfeedermission",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playermainmission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playermainmission",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playermainmission",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playerpopularwillmission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerpopularwillmission",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playerpopularwillmission",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playerpopularwillmission",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerpopularwillmission",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="playerpopularwillmission",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="playerpushinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerpushinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playerpushinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playerpushinfo",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerpushinfo",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="playerpushinfo",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="playersharemission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playersharemission",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playersharemission",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playersnspushinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playersnspushinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playersnspushinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playerworldexploreevent",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerworldexploreevent",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playerworldexploreevent",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playerworldexplorerecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerworldexplorerecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="playerworldexplorerecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="playerworldexplorerecord",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="playerworldexplorerecord",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="playerworldexplorerecord",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="redpack",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="redpack",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="redpack",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="regioncapitalnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="regioncapitalnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="regioncapitalnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="regioncapitalrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="regioncapitalrecord",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="regioncapitalrecord",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="resourceoutput",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="resourceoutput",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="resourceoutput",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="resourceoutput",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="resourceoutput",command="update",database="gameserver_1",result="success"} 4.0
mongodb_command_total{application="icefire-game",collection="resourceoutput",command="update",database="gameserver_1",result="total"} 4.0
mongodb_command_total{application="icefire-game",collection="role",command="count",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="role",command="count",database="gameserver_1",result="success"} 3.0
mongodb_command_total{application="icefire-game",collection="role",command="count",database="gameserver_1",result="total"} 3.0
mongodb_command_total{application="icefire-game",collection="role",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="role",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="role",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="role",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="role",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="role",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="roleabstamp",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleabstamp",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleabstamp",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleactivitymission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleactivitymission",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleactivitymission",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleactivitymission",command="getMore",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleactivitymission",command="getMore",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="roleactivitymission",command="getMore",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolealliancerecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolealliancerecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolealliancerecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolealliancerecord",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolealliancerecord",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolealliancerecord",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolearena",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolearena",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolearena",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleattackdongzhuobossrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleattackdongzhuobossrecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleattackdongzhuobossrecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolebattlepass",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolebattlepass",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolebattlepass",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleblizzard",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleblizzard",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleblizzard",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleblizzardresist",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleblizzardresist",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleblizzardresist",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolebrotherhoodinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolebrotherhoodinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolebrotherhoodinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolebuildsearchprogress",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolebuildsearchprogress",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolebuildsearchprogress",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolecache",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolecache",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolecache",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolecity",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolecity",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolecity",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolecity",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolecity",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolecity",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolecontinuousrecharge",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolecontinuousrecharge",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolecontinuousrecharge",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolecreatezonegridindex",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolecreatezonegridindex",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolecreatezonegridindex",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolecsabattle",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolecsabattle",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolecsabattle",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolecsamission",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolecsamission",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolecsamission",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roledailyrecharge",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roledailyrecharge",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roledailyrecharge",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roledevice",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roledevice",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roledevice",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roledevice",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roledevice",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="roledevice",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="roleenlistment",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleenlistment",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleenlistment",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleequip",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleequip",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleequip",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleexpedition",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleexpedition",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleexpedition",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleexpeditionmul",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleexpeditionmul",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleexpeditionmul",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleexpeditionnumbergate",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleexpeditionnumbergate",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleexpeditionnumbergate",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleextra",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleextra",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleextra",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleextra",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleextra",command="update",database="gameserver_1",result="success"} 3.0
mongodb_command_total{application="icefire-game",collection="roleextra",command="update",database="gameserver_1",result="total"} 3.0
mongodb_command_total{application="icefire-game",collection="rolefreetreasurebox",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolefreetreasurebox",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolefreetreasurebox",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolefreetreasurebox",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolefreetreasurebox",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolefreetreasurebox",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="roleguide",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleguide",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleguide",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleguide",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleguide",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="roleguide",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="roleheadinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleheadinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleheadinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolehorse",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolehorse",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolehorse",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolelibaofilterinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolelibaofilterinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolelibaofilterinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolelibaofilterinfo",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolelibaofilterinfo",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolelibaofilterinfo",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolelibaorecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolelibaorecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolelibaorecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolelibaorecord",command="getMore",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolelibaorecord",command="getMore",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolelibaorecord",command="getMore",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolelibaorecord",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolelibaorecord",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolelibaorecord",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolelordtreasure",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolelordtreasure",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolelordtreasure",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolelottery",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolelottery",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolelottery",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolelottery",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolelottery",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolelottery",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolemigraterecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolemigraterecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolemigraterecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolemonthcard",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolemonthcard",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolemonthcard",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolepeople",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolepeople",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolepeople",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolepeople",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolepeople",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolepeople",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolepopularskill",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolepopularskill",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolepopularskill",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolepopularskill",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolepopularskill",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolepopularskill",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolepopularwill",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolepopularwill",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolepopularwill",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolerechargelog",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolerechargelog",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolerechargelog",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolerecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolerecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolerecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolerecord",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolerecord",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolerecord",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="roleregioncapital",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleregioncapital",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleregioncapital",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolerobber",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolerobber",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolerobber",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleseasoninfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleseasoninfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleseasoninfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleseasontaskrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleseasontaskrecord",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="roleseasontaskrecord",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="roleserverinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleserverinfo",command="find",database="gameserver_1",result="success"} 4.0
mongodb_command_total{application="icefire-game",collection="roleserverinfo",command="find",database="gameserver_1",result="total"} 4.0
mongodb_command_total{application="icefire-game",collection="roleserverinfo",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleserverinfo",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="roleserverinfo",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolesetting",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolesetting",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolesetting",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolesevendayrecharge",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolesevendayrecharge",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolesevendayrecharge",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleshare",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleshare",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleshare",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleshare",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleshare",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="roleshare",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolestore",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolestore",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolestore",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolestore",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolestore",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="rolestore",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="roletitles",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roletitles",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roletitles",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleunlock",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleunlock",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="roleunlock",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="roleunlock",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="roleunlock",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="roleunlock",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="rolevip",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolevip",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolevip",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="rolewhispereractivityrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="rolewhispereractivityrecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="rolewhispereractivityrecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="ruinsnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="ruinsnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="ruinsnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="scienceinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="scienceinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="scienceinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="seasonwarmupdynastyinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="seasonwarmupdynastyinfo",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="seasonwarmupdynastyinfo",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="seasonwarmupplayerinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="seasonwarmupplayerinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="seasonwarmupplayerinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="serverinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="serverinfo",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="serverinfo",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="serverinfo",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="serverinfo",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="serverinfo",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="servermilestones",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="servermilestones",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="servermilestones",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="serverrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="serverrecord",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="serverrecord",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="sevencapturedailyinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="sevencapturedailyinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="sevencapturedailyinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="sevencapturedailyinfo",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="sevencapturedailyinfo",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="sevencapturedailyinfo",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="sevencapturenode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="sevencapturenode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="sevencapturenode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="shieldrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="shieldrecord",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="shieldrecord",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="siegeenginesnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="siegeenginesnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="siegeenginesnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="skindata",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="skindata",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="skindata",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="skindata",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="skindata",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="skindata",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="soldierinfo",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="soldierinfo",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="soldierinfo",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="soldierinfo",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="soldierinfo",command="update",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="soldierinfo",command="update",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="trusteeship",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="trusteeship",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="trusteeship",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="unactiverolemailrecord",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="unactiverolemailrecord",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="unactiverolemailrecord",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="whispererbossnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="whispererbossnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="whispererbossnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="whisperernode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="whisperernode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="whisperernode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="workprogress",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="workprogress",command="find",database="gameserver_1",result="success"} 2.0
mongodb_command_total{application="icefire-game",collection="workprogress",command="find",database="gameserver_1",result="total"} 2.0
mongodb_command_total{application="icefire-game",collection="worldbossdongzhuonode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="worldbossdongzhuonode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="worldbossdongzhuonode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="delete",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="delete",database="gameserver_1",result="success"} 140.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="delete",database="gameserver_1",result="total"} 140.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="find",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="getMore",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="getMore",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="getMore",database="gameserver_1",result="total"} 1.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="update",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="update",database="gameserver_1",result="success"} 94.0
mongodb_command_total{application="icefire-game",collection="worldbossnode",command="update",database="gameserver_1",result="total"} 94.0
mongodb_command_total{application="icefire-game",collection="worldcastlenode",command="find",database="gameserver_1",result="failure"} 0.0
mongodb_command_total{application="icefire-game",collection="worldcastlenode",command="find",database="gameserver_1",result="success"} 1.0
mongodb_command_total{application="icefire-game",collection="worldcastlenode",command="find",database="gameserver_1",result="total"} 1.0
# HELP mongodb_command_duration_seconds MongoDB command execution time
# TYPE mongodb_command_duration_seconds summary
mongodb_command_duration_seconds_count{application="icefire-game",collection="abstractemail",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="abstractemail",command="find",database="gameserver_1"} 0.0080136
mongodb_command_duration_seconds_count{application="icefire-game",collection="achieveinforecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="achieveinforecord",command="find",database="gameserver_1"} 0.0044312
mongodb_command_duration_seconds_count{application="icefire-game",collection="achievetaskrecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="achievetaskrecord",command="find",database="gameserver_1"} 0.0030181
mongodb_command_duration_seconds_count{application="icefire-game",collection="achievetaskrecord",command="getMore",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="achievetaskrecord",command="getMore",database="gameserver_1"} 0.0013349
mongodb_command_duration_seconds_count{application="icefire-game",collection="activity",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="activity",command="find",database="gameserver_1"} 0.0015895
mongodb_command_duration_seconds_count{application="icefire-game",collection="activity",command="update",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="activity",command="update",database="gameserver_1"} 0.0030162
mongodb_command_duration_seconds_count{application="icefire-game",collection="activityrecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="activityrecord",command="find",database="gameserver_1"} 0.0039169
mongodb_command_duration_seconds_count{application="icefire-game",collection="activityturn",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="activityturn",command="find",database="gameserver_1"} 0.0010087
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliance",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliance",command="find",database="gameserver_1"} 0.0010955
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliance",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliance",command="update",database="gameserver_1"} 0.0018733
mongodb_command_duration_seconds_count{application="icefire-game",collection="allianceaffair",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="allianceaffair",command="find",database="gameserver_1"} 0.0011469
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancebattleinfo",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancebattleinfo",command="find",database="gameserver_1"} 0.0010152
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancebattleplayerinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancebattleplayerinfo",command="find",database="gameserver_1"} 0.0020087
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancebossnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancebossnode",command="find",database="gameserver_1"} 0.0010776
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancebossroledonateinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancebossroledonateinfo",command="find",database="gameserver_1"} 0.003719
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancebuildingbase",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancebuildingbase",command="find",database="gameserver_1"} 0.0014579
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancebuildingnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancebuildingnode",command="find",database="gameserver_1"} 0.0014672
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancebuildingtech",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancebuildingtech",command="find",database="gameserver_1"} 0.0016218
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancecommonactivityinfo",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancecommonactivityinfo",command="find",database="gameserver_1"} 0.0011629
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancecommonranksnap",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancecommonranksnap",command="find",database="gameserver_1"} 9.085E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancefeasthallnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancefeasthallnode",command="find",database="gameserver_1"} 0.001302
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancefeastnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancefeastnode",command="find",database="gameserver_1"} 0.0012586
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancegift",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancegift",command="find",database="gameserver_1"} 0.0031402
mongodb_command_duration_seconds_count{application="icefire-game",collection="allianceleadermission",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="allianceleadermission",command="find",database="gameserver_1"} 0.0013147
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancelog",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancelog",command="find",database="gameserver_1"} 0.001659
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancemark",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancemark",command="find",database="gameserver_1"} 9.592E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancemember",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancemember",command="find",database="gameserver_1"} 0.0011584
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancemessagebord",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancemessagebord",command="find",database="gameserver_1"} 0.0014157
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancemissionbase",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancemissionbase",command="find",database="gameserver_1"} 0.0015933
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancemissionmemberinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancemissionmemberinfo",command="find",database="gameserver_1"} 0.0020451
mongodb_command_duration_seconds_count{application="icefire-game",collection="allianceoprecord",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="allianceoprecord",command="find",database="gameserver_1"} 0.0019663
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancereplace",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancereplace",command="find",database="gameserver_1"} 0.0016584
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancerequest",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancerequest",command="find",database="gameserver_1"} 0.0014632
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancesetting",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancesetting",command="find",database="gameserver_1"} 0.0019953
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancetech",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancetech",command="find",database="gameserver_1"} 0.0016585
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancewar",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancewar",command="find",database="gameserver_1"} 0.0016093
mongodb_command_duration_seconds_count{application="icefire-game",collection="alliancewhispereractivityrecord",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="alliancewhispereractivityrecord",command="find",database="gameserver_1"} 0.001372
mongodb_command_duration_seconds_count{application="icefire-game",collection="armyinfo",command="find",database="gameserver_1"} 3
mongodb_command_duration_seconds_sum{application="icefire-game",collection="armyinfo",command="find",database="gameserver_1"} 0.0037936
mongodb_command_duration_seconds_count{application="icefire-game",collection="armypreprogrammed",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="armypreprogrammed",command="find",database="gameserver_1"} 0.0104524
mongodb_command_duration_seconds_count{application="icefire-game",collection="armypresetinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="armypresetinfo",command="find",database="gameserver_1"} 0.0099855
mongodb_command_duration_seconds_count{application="icefire-game",collection="barbariancitynode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="barbariancitynode",command="find",database="gameserver_1"} 9.417E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="bingoactivitycontext",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="bingoactivitycontext",command="find",database="gameserver_1"} 0.002097
mongodb_command_duration_seconds_count{application="icefire-game",collection="buff",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="buff",command="find",database="gameserver_1"} 0.0040294
mongodb_command_duration_seconds_count{application="icefire-game",collection="caravan",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="caravan",command="find",database="gameserver_1"} 0.0020965
mongodb_command_duration_seconds_count{application="icefire-game",collection="caravantrade",command="find",database="gameserver_1"} 3
mongodb_command_duration_seconds_sum{application="icefire-game",collection="caravantrade",command="find",database="gameserver_1"} 0.0049154
mongodb_command_duration_seconds_count{application="icefire-game",collection="citybuild",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="citybuild",command="find",database="gameserver_1"} 0.002789
mongodb_command_duration_seconds_count{application="icefire-game",collection="citybuild",command="getMore",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="citybuild",command="getMore",database="gameserver_1"} 0.0028722
mongodb_command_duration_seconds_count{application="icefire-game",collection="commonmissioninfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="commonmissioninfo",command="find",database="gameserver_1"} 0.0035786
mongodb_command_duration_seconds_count{application="icefire-game",collection="csaactivityhistory",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="csaactivityhistory",command="find",database="gameserver_1"} 0.001538
mongodb_command_duration_seconds_count{application="icefire-game",collection="csagameserverresult",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="csagameserverresult",command="find",database="gameserver_1"} 0.0010907
mongodb_command_duration_seconds_count{application="icefire-game",collection="csaserverbattleinfo",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="csaserverbattleinfo",command="find",database="gameserver_1"} 7.56E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="csaservertrophy",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="csaservertrophy",command="find",database="gameserver_1"} 0.0012687
mongodb_command_duration_seconds_count{application="icefire-game",collection="dongzhuobossbox",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="dongzhuobossbox",command="find",database="gameserver_1"} 0.0010743
mongodb_command_duration_seconds_count{application="icefire-game",collection="gvgadmissionpush",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="gvgadmissionpush",command="find",database="gameserver_1"} 0.0013601
mongodb_command_duration_seconds_count{application="icefire-game",collection="gvgalliancebattlesummary",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="gvgalliancebattlesummary",command="find",database="gameserver_1"} 0.0017057
mongodb_command_duration_seconds_count{application="icefire-game",collection="gvgcupapplyinfo",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="gvgcupapplyinfo",command="find",database="gameserver_1"} 0.0012794
mongodb_command_duration_seconds_count{application="icefire-game",collection="gvgqualifiedalliance",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="gvgqualifiedalliance",command="find",database="gameserver_1"} 0.0015207
mongodb_command_duration_seconds_count{application="icefire-game",collection="gvgrolearenainfo",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="gvgrolearenainfo",command="find",database="gameserver_1"} 0.0010261
mongodb_command_duration_seconds_count{application="icefire-game",collection="gvgroleinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="gvgroleinfo",command="find",database="gameserver_1"} 0.0016637
mongodb_command_duration_seconds_count{application="icefire-game",collection="gvgrolewillstatusinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="gvgrolewillstatusinfo",command="find",database="gameserver_1"} 0.001966
mongodb_command_duration_seconds_count{application="icefire-game",collection="hero",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="hero",command="find",database="gameserver_1"} 0.0022458
mongodb_command_duration_seconds_count{application="icefire-game",collection="horsemating",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="horsemating",command="find",database="gameserver_1"} 0.004316
mongodb_command_duration_seconds_count{application="icefire-game",collection="horsemating",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="horsemating",command="update",database="gameserver_1"} 0.0022325
mongodb_command_duration_seconds_count{application="icefire-game",collection="icerolewheelinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="icerolewheelinfo",command="find",database="gameserver_1"} 0.0014414
mongodb_command_duration_seconds_count{application="icefire-game",collection="id_1",command="update",database="gameserver_1"} 3
mongodb_command_duration_seconds_sum{application="icefire-game",collection="id_1",command="update",database="gameserver_1"} 0.0044402
mongodb_command_duration_seconds_count{application="icefire-game",collection="importreward",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="importreward",command="find",database="gameserver_1"} 0.002455
mongodb_command_duration_seconds_count{application="icefire-game",collection="integratedspringboard",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="integratedspringboard",command="find",database="gameserver_1"} 8.944E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="item",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="item",command="find",database="gameserver_1"} 0.003609
mongodb_command_duration_seconds_count{application="icefire-game",collection="item",command="getMore",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="item",command="getMore",database="gameserver_1"} 0.0013361
mongodb_command_duration_seconds_count{application="icefire-game",collection="itemguarantee",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="itemguarantee",command="find",database="gameserver_1"} 0.0016049
mongodb_command_duration_seconds_count{application="icefire-game",collection="itemrecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="itemrecord",command="find",database="gameserver_1"} 0.0024924
mongodb_command_duration_seconds_count{application="icefire-game",collection="kingdombuff",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="kingdombuff",command="find",database="gameserver_1"} 0.0010014
mongodb_command_duration_seconds_count{application="icefire-game",collection="kvkhistoryallianceprosperity",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="kvkhistoryallianceprosperity",command="find",database="gameserver_1"} 0.0012702
mongodb_command_duration_seconds_count{application="icefire-game",collection="kvkhistorylegionprosperity",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="kvkhistorylegionprosperity",command="find",database="gameserver_1"} 0.001196
mongodb_command_duration_seconds_count{application="icefire-game",collection="legionmilestone",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="legionmilestone",command="find",database="gameserver_1"} 0.0010278
mongodb_command_duration_seconds_count{application="icefire-game",collection="loginrewardrecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="loginrewardrecord",command="find",database="gameserver_1"} 0.0023738
mongodb_command_duration_seconds_count{application="icefire-game",collection="milestone",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="milestone",command="find",database="gameserver_1"} 0.0014393
mongodb_command_duration_seconds_count{application="icefire-game",collection="missionbossnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="missionbossnode",command="find",database="gameserver_1"} 9.263E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="newplayermission",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="newplayermission",command="find",database="gameserver_1"} 0.0034239
mongodb_command_duration_seconds_count{application="icefire-game",collection="newplayermission",command="update",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="newplayermission",command="update",database="gameserver_1"} 0.0011854
mongodb_command_duration_seconds_count{application="icefire-game",collection="newresnode",command="delete",database="gameserver_1"} 140
mongodb_command_duration_seconds_sum{application="icefire-game",collection="newresnode",command="delete",database="gameserver_1"} 0.2023431
mongodb_command_duration_seconds_count{application="icefire-game",collection="newresnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="newresnode",command="find",database="gameserver_1"} 0.0020364
mongodb_command_duration_seconds_count{application="icefire-game",collection="newresnode",command="getMore",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="newresnode",command="getMore",database="gameserver_1"} 0.1405091
mongodb_command_duration_seconds_count{application="icefire-game",collection="newresnode",command="update",database="gameserver_1"} 64
mongodb_command_duration_seconds_sum{application="icefire-game",collection="newresnode",command="update",database="gameserver_1"} 0.0988915
mongodb_command_duration_seconds_count{application="icefire-game",collection="newstrongestlordsplan",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="newstrongestlordsplan",command="find",database="gameserver_1"} 0.0012332
mongodb_command_duration_seconds_count{application="icefire-game",collection="newstrongestplayerinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="newstrongestplayerinfo",command="find",database="gameserver_1"} 0.0025677
mongodb_command_duration_seconds_count{application="icefire-game",collection="npccenternode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="npccenternode",command="find",database="gameserver_1"} 0.001371
mongodb_command_duration_seconds_count{application="icefire-game",collection="npcnode",command="delete",database="gameserver_1"} 10
mongodb_command_duration_seconds_sum{application="icefire-game",collection="npcnode",command="delete",database="gameserver_1"} 0.0104433
mongodb_command_duration_seconds_count{application="icefire-game",collection="npcnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="npcnode",command="find",database="gameserver_1"} 0.0017853
mongodb_command_duration_seconds_count{application="icefire-game",collection="npcnode",command="getMore",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="npcnode",command="getMore",database="gameserver_1"} 0.0306269
mongodb_command_duration_seconds_count{application="icefire-game",collection="npcnode",command="update",database="gameserver_1"} 8
mongodb_command_duration_seconds_sum{application="icefire-game",collection="npcnode",command="update",database="gameserver_1"} 0.0113981
mongodb_command_duration_seconds_count{application="icefire-game",collection="officialsinfo",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="officialsinfo",command="find",database="gameserver_1"} 0.0010717
mongodb_command_duration_seconds_count{application="icefire-game",collection="officialsinfo",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="officialsinfo",command="update",database="gameserver_1"} 0.0016447
mongodb_command_duration_seconds_count{application="icefire-game",collection="onlinerewarddata",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="onlinerewarddata",command="find",database="gameserver_1"} 0.0025604
mongodb_command_duration_seconds_count{application="icefire-game",collection="onlinerewarddata",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="onlinerewarddata",command="update",database="gameserver_1"} 8.088E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="personalmilestone",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="personalmilestone",command="find",database="gameserver_1"} 0.0019768
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerchaptermission",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerchaptermission",command="find",database="gameserver_1"} 0.0033228
mongodb_command_duration_seconds_count{application="icefire-game",collection="playercommonactivityinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playercommonactivityinfo",command="find",database="gameserver_1"} 0.0037447
mongodb_command_duration_seconds_count{application="icefire-game",collection="playercommonranksnap",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playercommonranksnap",command="find",database="gameserver_1"} 7.417E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerdailymission",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerdailymission",command="find",database="gameserver_1"} 0.0017975
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerfeedermission",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerfeedermission",command="find",database="gameserver_1"} 0.0143854
mongodb_command_duration_seconds_count{application="icefire-game",collection="playermainmission",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playermainmission",command="find",database="gameserver_1"} 0.0034142
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerpopularwillmission",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerpopularwillmission",command="find",database="gameserver_1"} 0.0036372
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerpopularwillmission",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerpopularwillmission",command="update",database="gameserver_1"} 9.912E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerpushinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerpushinfo",command="find",database="gameserver_1"} 0.0064596
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerpushinfo",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerpushinfo",command="update",database="gameserver_1"} 0.0017563
mongodb_command_duration_seconds_count{application="icefire-game",collection="playersharemission",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playersharemission",command="find",database="gameserver_1"} 0.0021068
mongodb_command_duration_seconds_count{application="icefire-game",collection="playersnspushinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playersnspushinfo",command="find",database="gameserver_1"} 0.0018723
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerworldexploreevent",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerworldexploreevent",command="find",database="gameserver_1"} 0.0022731
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerworldexplorerecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerworldexplorerecord",command="find",database="gameserver_1"} 0.0026477
mongodb_command_duration_seconds_count{application="icefire-game",collection="playerworldexplorerecord",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="playerworldexplorerecord",command="update",database="gameserver_1"} 7.073E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="redpack",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="redpack",command="find",database="gameserver_1"} 0.0018786
mongodb_command_duration_seconds_count{application="icefire-game",collection="regioncapitalnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="regioncapitalnode",command="find",database="gameserver_1"} 0.0024874
mongodb_command_duration_seconds_count{application="icefire-game",collection="regioncapitalrecord",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="regioncapitalrecord",command="find",database="gameserver_1"} 0.001341
mongodb_command_duration_seconds_count{application="icefire-game",collection="resourceoutput",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="resourceoutput",command="find",database="gameserver_1"} 0.0025279
mongodb_command_duration_seconds_count{application="icefire-game",collection="resourceoutput",command="update",database="gameserver_1"} 4
mongodb_command_duration_seconds_sum{application="icefire-game",collection="resourceoutput",command="update",database="gameserver_1"} 0.007335
mongodb_command_duration_seconds_count{application="icefire-game",collection="role",command="count",database="gameserver_1"} 3
mongodb_command_duration_seconds_sum{application="icefire-game",collection="role",command="count",database="gameserver_1"} 0.0033317
mongodb_command_duration_seconds_count{application="icefire-game",collection="role",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="role",command="find",database="gameserver_1"} 0.0021312
mongodb_command_duration_seconds_count{application="icefire-game",collection="role",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="role",command="update",database="gameserver_1"} 0.0012241
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleabstamp",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleabstamp",command="find",database="gameserver_1"} 0.0028186
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleactivitymission",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleactivitymission",command="find",database="gameserver_1"} 0.0024281
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleactivitymission",command="getMore",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleactivitymission",command="getMore",database="gameserver_1"} 0.002188
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolealliancerecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolealliancerecord",command="find",database="gameserver_1"} 0.0019264
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolealliancerecord",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolealliancerecord",command="update",database="gameserver_1"} 8.129E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolearena",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolearena",command="find",database="gameserver_1"} 0.003382
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleattackdongzhuobossrecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleattackdongzhuobossrecord",command="find",database="gameserver_1"} 0.0056824
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolebattlepass",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolebattlepass",command="find",database="gameserver_1"} 0.0020267
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleblizzard",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleblizzard",command="find",database="gameserver_1"} 0.0022296
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleblizzardresist",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleblizzardresist",command="find",database="gameserver_1"} 0.0040669
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolebrotherhoodinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolebrotherhoodinfo",command="find",database="gameserver_1"} 0.0022829
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolebuildsearchprogress",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolebuildsearchprogress",command="find",database="gameserver_1"} 0.0027293
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolecache",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolecache",command="find",database="gameserver_1"} 0.0010698
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolecity",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolecity",command="find",database="gameserver_1"} 0.0051075
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolecity",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolecity",command="update",database="gameserver_1"} 9.843E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolecontinuousrecharge",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolecontinuousrecharge",command="find",database="gameserver_1"} 0.0026449
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolecreatezonegridindex",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolecreatezonegridindex",command="find",database="gameserver_1"} 0.0013883
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolecsabattle",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolecsabattle",command="find",database="gameserver_1"} 0.0013234
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolecsamission",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolecsamission",command="find",database="gameserver_1"} 0.0012204
mongodb_command_duration_seconds_count{application="icefire-game",collection="roledailyrecharge",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roledailyrecharge",command="find",database="gameserver_1"} 0.0025179
mongodb_command_duration_seconds_count{application="icefire-game",collection="roledevice",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roledevice",command="find",database="gameserver_1"} 0.0023024
mongodb_command_duration_seconds_count{application="icefire-game",collection="roledevice",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roledevice",command="update",database="gameserver_1"} 6.272E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleenlistment",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleenlistment",command="find",database="gameserver_1"} 0.0023144
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleequip",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleequip",command="find",database="gameserver_1"} 0.002296
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleexpedition",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleexpedition",command="find",database="gameserver_1"} 0.0042265
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleexpeditionmul",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleexpeditionmul",command="find",database="gameserver_1"} 0.0039525
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleexpeditionnumbergate",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleexpeditionnumbergate",command="find",database="gameserver_1"} 0.0104778
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleextra",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleextra",command="find",database="gameserver_1"} 0.0035423
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleextra",command="update",database="gameserver_1"} 3
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleextra",command="update",database="gameserver_1"} 0.0055266
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolefreetreasurebox",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolefreetreasurebox",command="find",database="gameserver_1"} 0.0024761
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolefreetreasurebox",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolefreetreasurebox",command="update",database="gameserver_1"} 8.206E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleguide",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleguide",command="find",database="gameserver_1"} 0.0020641
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleguide",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleguide",command="update",database="gameserver_1"} 8.449E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleheadinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleheadinfo",command="find",database="gameserver_1"} 0.0025299
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolehorse",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolehorse",command="find",database="gameserver_1"} 0.0019331
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolelibaofilterinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolelibaofilterinfo",command="find",database="gameserver_1"} 0.0037466
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolelibaofilterinfo",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolelibaofilterinfo",command="update",database="gameserver_1"} 7.126E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolelibaorecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolelibaorecord",command="find",database="gameserver_1"} 0.0028144
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolelibaorecord",command="getMore",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolelibaorecord",command="getMore",database="gameserver_1"} 0.002322
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolelibaorecord",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolelibaorecord",command="update",database="gameserver_1"} 0.0067589
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolelordtreasure",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolelordtreasure",command="find",database="gameserver_1"} 0.0098667
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolelottery",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolelottery",command="find",database="gameserver_1"} 0.0033423
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolelottery",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolelottery",command="update",database="gameserver_1"} 4.679E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolemigraterecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolemigraterecord",command="find",database="gameserver_1"} 0.0025288
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolemonthcard",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolemonthcard",command="find",database="gameserver_1"} 0.003154
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolepeople",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolepeople",command="find",database="gameserver_1"} 0.0037669
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolepeople",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolepeople",command="update",database="gameserver_1"} 0.0011055
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolepopularskill",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolepopularskill",command="find",database="gameserver_1"} 0.0027828
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolepopularskill",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolepopularskill",command="update",database="gameserver_1"} 0.0011487
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolepopularwill",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolepopularwill",command="find",database="gameserver_1"} 0.0033088
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolerechargelog",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolerechargelog",command="find",database="gameserver_1"} 0.0028345
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolerecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolerecord",command="find",database="gameserver_1"} 0.0019456
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolerecord",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolerecord",command="update",database="gameserver_1"} 5.438E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleregioncapital",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleregioncapital",command="find",database="gameserver_1"} 0.0039448
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolerobber",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolerobber",command="find",database="gameserver_1"} 0.0018706
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleseasoninfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleseasoninfo",command="find",database="gameserver_1"} 0.0039991
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleseasontaskrecord",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleseasontaskrecord",command="find",database="gameserver_1"} 0.0063438
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleserverinfo",command="find",database="gameserver_1"} 4
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleserverinfo",command="find",database="gameserver_1"} 0.0339911
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleserverinfo",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleserverinfo",command="update",database="gameserver_1"} 0.001942
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolesetting",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolesetting",command="find",database="gameserver_1"} 0.002274
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolesevendayrecharge",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolesevendayrecharge",command="find",database="gameserver_1"} 0.0015548
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleshare",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleshare",command="find",database="gameserver_1"} 0.0037755
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleshare",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleshare",command="update",database="gameserver_1"} 6.379E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolestore",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolestore",command="find",database="gameserver_1"} 0.003438
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolestore",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolestore",command="update",database="gameserver_1"} 0.001959
mongodb_command_duration_seconds_count{application="icefire-game",collection="roletitles",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roletitles",command="find",database="gameserver_1"} 0.0019374
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleunlock",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleunlock",command="find",database="gameserver_1"} 0.0076469
mongodb_command_duration_seconds_count{application="icefire-game",collection="roleunlock",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="roleunlock",command="update",database="gameserver_1"} 6.023E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolevip",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolevip",command="find",database="gameserver_1"} 0.0024148
mongodb_command_duration_seconds_count{application="icefire-game",collection="rolewhispereractivityrecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="rolewhispereractivityrecord",command="find",database="gameserver_1"} 0.0027738
mongodb_command_duration_seconds_count{application="icefire-game",collection="ruinsnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="ruinsnode",command="find",database="gameserver_1"} 0.0012
mongodb_command_duration_seconds_count{application="icefire-game",collection="scienceinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="scienceinfo",command="find",database="gameserver_1"} 0.0021045
mongodb_command_duration_seconds_count{application="icefire-game",collection="seasonwarmupdynastyinfo",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="seasonwarmupdynastyinfo",command="find",database="gameserver_1"} 0.0012854
mongodb_command_duration_seconds_count{application="icefire-game",collection="seasonwarmupplayerinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="seasonwarmupplayerinfo",command="find",database="gameserver_1"} 0.0019265
mongodb_command_duration_seconds_count{application="icefire-game",collection="serverinfo",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="serverinfo",command="find",database="gameserver_1"} 0.001306
mongodb_command_duration_seconds_count{application="icefire-game",collection="serverinfo",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="serverinfo",command="update",database="gameserver_1"} 0.0014732
mongodb_command_duration_seconds_count{application="icefire-game",collection="servermilestones",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="servermilestones",command="find",database="gameserver_1"} 9.869E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="serverrecord",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="serverrecord",command="find",database="gameserver_1"} 0.0014661
mongodb_command_duration_seconds_count{application="icefire-game",collection="sevencapturedailyinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="sevencapturedailyinfo",command="find",database="gameserver_1"} 0.0015708
mongodb_command_duration_seconds_count{application="icefire-game",collection="sevencapturedailyinfo",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="sevencapturedailyinfo",command="update",database="gameserver_1"} 5.821E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="sevencapturenode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="sevencapturenode",command="find",database="gameserver_1"} 0.0010715
mongodb_command_duration_seconds_count{application="icefire-game",collection="shieldrecord",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="shieldrecord",command="find",database="gameserver_1"} 0.003531
mongodb_command_duration_seconds_count{application="icefire-game",collection="siegeenginesnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="siegeenginesnode",command="find",database="gameserver_1"} 0.0013024
mongodb_command_duration_seconds_count{application="icefire-game",collection="skindata",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="skindata",command="find",database="gameserver_1"} 0.0041572
mongodb_command_duration_seconds_count{application="icefire-game",collection="skindata",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="skindata",command="update",database="gameserver_1"} 6.948E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="soldierinfo",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="soldierinfo",command="find",database="gameserver_1"} 0.0022667
mongodb_command_duration_seconds_count{application="icefire-game",collection="soldierinfo",command="update",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="soldierinfo",command="update",database="gameserver_1"} 8.12E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="trusteeship",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="trusteeship",command="find",database="gameserver_1"} 8.197E-4
mongodb_command_duration_seconds_count{application="icefire-game",collection="unactiverolemailrecord",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="unactiverolemailrecord",command="find",database="gameserver_1"} 0.0010394
mongodb_command_duration_seconds_count{application="icefire-game",collection="whispererbossnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="whispererbossnode",command="find",database="gameserver_1"} 0.0014189
mongodb_command_duration_seconds_count{application="icefire-game",collection="whisperernode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="whisperernode",command="find",database="gameserver_1"} 0.001674
mongodb_command_duration_seconds_count{application="icefire-game",collection="workprogress",command="find",database="gameserver_1"} 2
mongodb_command_duration_seconds_sum{application="icefire-game",collection="workprogress",command="find",database="gameserver_1"} 0.0025847
mongodb_command_duration_seconds_count{application="icefire-game",collection="worldbossdongzhuonode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="worldbossdongzhuonode",command="find",database="gameserver_1"} 0.0014865
mongodb_command_duration_seconds_count{application="icefire-game",collection="worldbossnode",command="delete",database="gameserver_1"} 140
mongodb_command_duration_seconds_sum{application="icefire-game",collection="worldbossnode",command="delete",database="gameserver_1"} 0.2186029
mongodb_command_duration_seconds_count{application="icefire-game",collection="worldbossnode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="worldbossnode",command="find",database="gameserver_1"} 0.0016459
mongodb_command_duration_seconds_count{application="icefire-game",collection="worldbossnode",command="getMore",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="worldbossnode",command="getMore",database="gameserver_1"} 0.0103499
mongodb_command_duration_seconds_count{application="icefire-game",collection="worldbossnode",command="update",database="gameserver_1"} 94
mongodb_command_duration_seconds_sum{application="icefire-game",collection="worldbossnode",command="update",database="gameserver_1"} 0.1611334
mongodb_command_duration_seconds_count{application="icefire-game",collection="worldcastlenode",command="find",database="gameserver_1"} 1
mongodb_command_duration_seconds_sum{application="icefire-game",collection="worldcastlenode",command="find",database="gameserver_1"} 0.0012431
# HELP mongodb_command_duration_seconds_max MongoDB command execution time
# TYPE mongodb_command_duration_seconds_max gauge
mongodb_command_duration_seconds_max{application="icefire-game",collection="abstractemail",command="find",database="gameserver_1"} 0.0080136
mongodb_command_duration_seconds_max{application="icefire-game",collection="achieveinforecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="achievetaskrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="achievetaskrecord",command="getMore",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="activity",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="activity",command="update",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="activityrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="activityturn",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliance",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliance",command="update",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="allianceaffair",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancebattleinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancebattleplayerinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancebossnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancebossroledonateinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancebuildingbase",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancebuildingnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancebuildingtech",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancecommonactivityinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancecommonranksnap",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancefeasthallnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancefeastnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancegift",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="allianceleadermission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancelog",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancemark",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancemember",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancemessagebord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancemissionbase",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancemissionmemberinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="allianceoprecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancereplace",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancerequest",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancesetting",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancetech",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancewar",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="alliancewhispereractivityrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="armyinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="armypreprogrammed",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="armypresetinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="barbariancitynode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="bingoactivitycontext",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="buff",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="caravan",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="caravantrade",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="citybuild",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="citybuild",command="getMore",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="commonmissioninfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="csaactivityhistory",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="csagameserverresult",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="csaserverbattleinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="csaservertrophy",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="dongzhuobossbox",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="gvgadmissionpush",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="gvgalliancebattlesummary",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="gvgcupapplyinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="gvgqualifiedalliance",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="gvgrolearenainfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="gvgroleinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="gvgrolewillstatusinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="hero",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="horsemating",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="horsemating",command="update",database="gameserver_1"} 0.0022325
mongodb_command_duration_seconds_max{application="icefire-game",collection="icerolewheelinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="id_1",command="update",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="importreward",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="integratedspringboard",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="item",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="item",command="getMore",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="itemguarantee",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="itemrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="kingdombuff",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="kvkhistoryallianceprosperity",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="kvkhistorylegionprosperity",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="legionmilestone",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="loginrewardrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="milestone",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="missionbossnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="newplayermission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="newplayermission",command="update",database="gameserver_1"} 5.964E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="newresnode",command="delete",database="gameserver_1"} 0.0082491
mongodb_command_duration_seconds_max{application="icefire-game",collection="newresnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="newresnode",command="getMore",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="newresnode",command="update",database="gameserver_1"} 0.0071893
mongodb_command_duration_seconds_max{application="icefire-game",collection="newstrongestlordsplan",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="newstrongestplayerinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="npccenternode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="npcnode",command="delete",database="gameserver_1"} 0.0012606
mongodb_command_duration_seconds_max{application="icefire-game",collection="npcnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="npcnode",command="getMore",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="npcnode",command="update",database="gameserver_1"} 0.0021019
mongodb_command_duration_seconds_max{application="icefire-game",collection="officialsinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="officialsinfo",command="update",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="onlinerewarddata",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="onlinerewarddata",command="update",database="gameserver_1"} 8.088E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="personalmilestone",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerchaptermission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playercommonactivityinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playercommonranksnap",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerdailymission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerfeedermission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playermainmission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerpopularwillmission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerpopularwillmission",command="update",database="gameserver_1"} 9.912E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerpushinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerpushinfo",command="update",database="gameserver_1"} 0.0017563
mongodb_command_duration_seconds_max{application="icefire-game",collection="playersharemission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playersnspushinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerworldexploreevent",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerworldexplorerecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="playerworldexplorerecord",command="update",database="gameserver_1"} 7.073E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="redpack",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="regioncapitalnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="regioncapitalrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="resourceoutput",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="resourceoutput",command="update",database="gameserver_1"} 0.0045516
mongodb_command_duration_seconds_max{application="icefire-game",collection="role",command="count",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="role",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="role",command="update",database="gameserver_1"} 0.0012241
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleabstamp",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleactivitymission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleactivitymission",command="getMore",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolealliancerecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolealliancerecord",command="update",database="gameserver_1"} 8.129E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolearena",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleattackdongzhuobossrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolebattlepass",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleblizzard",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleblizzardresist",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolebrotherhoodinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolebuildsearchprogress",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolecache",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolecity",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolecity",command="update",database="gameserver_1"} 9.843E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolecontinuousrecharge",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolecreatezonegridindex",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolecsabattle",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolecsamission",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roledailyrecharge",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roledevice",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roledevice",command="update",database="gameserver_1"} 6.272E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleenlistment",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleequip",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleexpedition",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleexpeditionmul",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleexpeditionnumbergate",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleextra",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleextra",command="update",database="gameserver_1"} 0.001477
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolefreetreasurebox",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolefreetreasurebox",command="update",database="gameserver_1"} 8.206E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleguide",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleguide",command="update",database="gameserver_1"} 8.449E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleheadinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolehorse",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolelibaofilterinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolelibaofilterinfo",command="update",database="gameserver_1"} 7.126E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolelibaorecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolelibaorecord",command="getMore",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolelibaorecord",command="update",database="gameserver_1"} 0.0067589
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolelordtreasure",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolelottery",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolelottery",command="update",database="gameserver_1"} 4.679E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolemigraterecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolemonthcard",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolepeople",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolepeople",command="update",database="gameserver_1"} 0.0011055
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolepopularskill",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolepopularskill",command="update",database="gameserver_1"} 0.0011487
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolepopularwill",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolerechargelog",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolerecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolerecord",command="update",database="gameserver_1"} 5.438E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleregioncapital",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolerobber",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleseasoninfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleseasontaskrecord",command="find",database="gameserver_1"} 0.0063438
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleserverinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleserverinfo",command="update",database="gameserver_1"} 0.001942
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolesetting",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolesevendayrecharge",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleshare",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleshare",command="update",database="gameserver_1"} 6.379E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolestore",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolestore",command="update",database="gameserver_1"} 0.001959
mongodb_command_duration_seconds_max{application="icefire-game",collection="roletitles",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleunlock",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="roleunlock",command="update",database="gameserver_1"} 6.023E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolevip",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="rolewhispereractivityrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="ruinsnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="scienceinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="seasonwarmupdynastyinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="seasonwarmupplayerinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="serverinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="serverinfo",command="update",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="servermilestones",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="serverrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="sevencapturedailyinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="sevencapturedailyinfo",command="update",database="gameserver_1"} 5.821E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="sevencapturenode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="shieldrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="siegeenginesnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="skindata",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="skindata",command="update",database="gameserver_1"} 6.948E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="soldierinfo",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="soldierinfo",command="update",database="gameserver_1"} 8.12E-4
mongodb_command_duration_seconds_max{application="icefire-game",collection="trusteeship",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="unactiverolemailrecord",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="whispererbossnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="whisperernode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="workprogress",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="worldbossdongzhuonode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="worldbossnode",command="delete",database="gameserver_1"} 0.0029774
mongodb_command_duration_seconds_max{application="icefire-game",collection="worldbossnode",command="find",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="worldbossnode",command="getMore",database="gameserver_1"} 0.0
mongodb_command_duration_seconds_max{application="icefire-game",collection="worldbossnode",command="update",database="gameserver_1"} 0.0064706
mongodb_command_duration_seconds_max{application="icefire-game",collection="worldcastlenode",command="find",database="gameserver_1"} 0.0
# HELP process_cpu_time_ns_total The "cpu time" used by the Java Virtual Machine process
# TYPE process_cpu_time_ns_total counter
process_cpu_time_ns_total{application="icefire-game"} 9.140625E10
# HELP process_cpu_usage The "recent cpu usage" for the Java Virtual Machine process
# TYPE process_cpu_usage gauge
process_cpu_usage{application="icefire-game"} 0.008656274705008007
# HELP process_start_time_seconds Start time of the process since unix epoch.
# TYPE process_start_time_seconds gauge
process_start_time_seconds{application="icefire-game"} 1.753930351442E9
# HELP process_uptime_seconds The uptime of the Java virtual machine
# TYPE process_uptime_seconds gauge
process_uptime_seconds{application="icefire-game"} 321.476
# HELP redis_operation_total Redis operation execution count
# TYPE redis_operation_total counter
redis_operation_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zadd",result="success"} 100.0
redis_operation_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zadd",result="total"} 100.0
redis_operation_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrangewithscores",result="success"} 78.0
redis_operation_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrangewithscores",result="total"} 78.0
redis_operation_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrank",result="success"} 6.0
redis_operation_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrank",result="total"} 6.0
redis_operation_total{application="icefire-game",dataType="单服普通",operation="zadd",result="success"} 88.0
redis_operation_total{application="icefire-game",dataType="单服普通",operation="zadd",result="total"} 88.0
redis_operation_total{application="icefire-game",dataType="单服普通",operation="zrem",result="success"} 4.0
redis_operation_total{application="icefire-game",dataType="单服普通",operation="zrem",result="total"} 4.0
redis_operation_total{application="icefire-game",dataType="单服普通",operation="zrevrangewithscores",result="success"} 123.0
redis_operation_total{application="icefire-game",dataType="单服普通",operation="zrevrangewithscores",result="total"} 123.0
redis_operation_total{application="icefire-game",dataType="游戏服Instance数据",operation="hset",result="success"} 31.0
redis_operation_total{application="icefire-game",dataType="游戏服Instance数据",operation="hset",result="total"} 31.0
redis_operation_total{application="icefire-game",dataType="玩家邀请码",operation="get",result="success"} 1.0
redis_operation_total{application="icefire-game",dataType="玩家邀请码",operation="get",result="total"} 1.0
redis_operation_total{application="icefire-game",dataType="竞技场分组",operation="get",result="success"} 32.0
redis_operation_total{application="icefire-game",dataType="竞技场分组",operation="get",result="total"} 32.0
redis_operation_total{application="icefire-game",dataType="竞技场分组",operation="hget",result="success"} 93.0
redis_operation_total{application="icefire-game",dataType="竞技场分组",operation="hget",result="total"} 93.0
redis_operation_total{application="icefire-game",dataType="竞技场分组",operation="zrevrangewithscores",result="success"} 1.0
redis_operation_total{application="icefire-game",dataType="竞技场分组",operation="zrevrangewithscores",result="total"} 1.0
redis_operation_total{application="icefire-game",dataType="账号服普通",operation="get",result="success"} 1.0
redis_operation_total{application="icefire-game",dataType="账号服普通",operation="get",result="total"} 1.0
redis_operation_total{application="icefire-game",dataType="账号服普通",operation="hgetall",result="success"} 1.0
redis_operation_total{application="icefire-game",dataType="账号服普通",operation="hgetall",result="total"} 1.0
redis_operation_total{application="icefire-game",dataType="账号服普通",operation="set",result="success"} 1.0
redis_operation_total{application="icefire-game",dataType="账号服普通",operation="set",result="total"} 1.0
redis_operation_total{application="icefire-game",dataType="跨服排名",operation="zrevrangewithscores",result="success"} 14.0
redis_operation_total{application="icefire-game",dataType="跨服排名",operation="zrevrangewithscores",result="total"} 14.0
# HELP redis_operation_duration_seconds Redis operation execution time
# TYPE redis_operation_duration_seconds summary
redis_operation_duration_seconds_count{application="icefire-game",dataType="全服排行榜(8个服)",operation="zadd"} 100
redis_operation_duration_seconds_sum{application="icefire-game",dataType="全服排行榜(8个服)",operation="zadd"} 0.135
redis_operation_duration_seconds_count{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrangewithscores"} 78
redis_operation_duration_seconds_sum{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrangewithscores"} 0.056
redis_operation_duration_seconds_count{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrank"} 6
redis_operation_duration_seconds_sum{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrank"} 0.009
redis_operation_duration_seconds_count{application="icefire-game",dataType="单服普通",operation="zadd"} 88
redis_operation_duration_seconds_sum{application="icefire-game",dataType="单服普通",operation="zadd"} 0.089
redis_operation_duration_seconds_count{application="icefire-game",dataType="单服普通",operation="zrem"} 4
redis_operation_duration_seconds_sum{application="icefire-game",dataType="单服普通",operation="zrem"} 0.004
redis_operation_duration_seconds_count{application="icefire-game",dataType="单服普通",operation="zrevrangewithscores"} 123
redis_operation_duration_seconds_sum{application="icefire-game",dataType="单服普通",operation="zrevrangewithscores"} 0.073
redis_operation_duration_seconds_count{application="icefire-game",dataType="游戏服Instance数据",operation="hset"} 31
redis_operation_duration_seconds_sum{application="icefire-game",dataType="游戏服Instance数据",operation="hset"} 0.062
redis_operation_duration_seconds_count{application="icefire-game",dataType="玩家邀请码",operation="get"} 1
redis_operation_duration_seconds_sum{application="icefire-game",dataType="玩家邀请码",operation="get"} 0.016
redis_operation_duration_seconds_count{application="icefire-game",dataType="竞技场分组",operation="get"} 32
redis_operation_duration_seconds_sum{application="icefire-game",dataType="竞技场分组",operation="get"} 0.068
redis_operation_duration_seconds_count{application="icefire-game",dataType="竞技场分组",operation="hget"} 93
redis_operation_duration_seconds_sum{application="icefire-game",dataType="竞技场分组",operation="hget"} 0.231
redis_operation_duration_seconds_count{application="icefire-game",dataType="竞技场分组",operation="zrevrangewithscores"} 1
redis_operation_duration_seconds_sum{application="icefire-game",dataType="竞技场分组",operation="zrevrangewithscores"} 0.0
redis_operation_duration_seconds_count{application="icefire-game",dataType="账号服普通",operation="get"} 1
redis_operation_duration_seconds_sum{application="icefire-game",dataType="账号服普通",operation="get"} 0.005
redis_operation_duration_seconds_count{application="icefire-game",dataType="账号服普通",operation="hgetall"} 1
redis_operation_duration_seconds_sum{application="icefire-game",dataType="账号服普通",operation="hgetall"} 0.001
redis_operation_duration_seconds_count{application="icefire-game",dataType="账号服普通",operation="set"} 1
redis_operation_duration_seconds_sum{application="icefire-game",dataType="账号服普通",operation="set"} 0.004
redis_operation_duration_seconds_count{application="icefire-game",dataType="跨服排名",operation="zrevrangewithscores"} 14
redis_operation_duration_seconds_sum{application="icefire-game",dataType="跨服排名",operation="zrevrangewithscores"} 0.013
# HELP redis_operation_duration_seconds_max Redis operation execution time
# TYPE redis_operation_duration_seconds_max gauge
redis_operation_duration_seconds_max{application="icefire-game",dataType="全服排行榜(8个服)",operation="zadd"} 0.003
redis_operation_duration_seconds_max{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrangewithscores"} 0.003
redis_operation_duration_seconds_max{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrank"} 0.003
redis_operation_duration_seconds_max{application="icefire-game",dataType="单服普通",operation="zadd"} 0.003
redis_operation_duration_seconds_max{application="icefire-game",dataType="单服普通",operation="zrem"} 0.001
redis_operation_duration_seconds_max{application="icefire-game",dataType="单服普通",operation="zrevrangewithscores"} 0.003
redis_operation_duration_seconds_max{application="icefire-game",dataType="游戏服Instance数据",operation="hset"} 0.002
redis_operation_duration_seconds_max{application="icefire-game",dataType="玩家邀请码",operation="get"} 0.016
redis_operation_duration_seconds_max{application="icefire-game",dataType="竞技场分组",operation="get"} 0.002
redis_operation_duration_seconds_max{application="icefire-game",dataType="竞技场分组",operation="hget"} 0.002
redis_operation_duration_seconds_max{application="icefire-game",dataType="竞技场分组",operation="zrevrangewithscores"} 0.0
redis_operation_duration_seconds_max{application="icefire-game",dataType="账号服普通",operation="get"} 0.005
redis_operation_duration_seconds_max{application="icefire-game",dataType="账号服普通",operation="hgetall"} 0.0
redis_operation_duration_seconds_max{application="icefire-game",dataType="账号服普通",operation="set"} 0.004
redis_operation_duration_seconds_max{application="icefire-game",dataType="跨服排名",operation="zrevrangewithscores"} 0.0
# HELP redis_operation_errors_total Redis operation error count
# TYPE redis_operation_errors_total counter
redis_operation_errors_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zadd",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrangewithscores",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrank",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="单服普通",operation="zadd",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="单服普通",operation="zrem",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="单服普通",operation="zrevrangewithscores",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="游戏服Instance数据",operation="hset",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="玩家邀请码",operation="get",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="竞技场分组",operation="get",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="竞技场分组",operation="hget",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="竞技场分组",operation="zrevrangewithscores",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="账号服普通",operation="get",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="账号服普通",operation="hgetall",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="账号服普通",operation="set",result="error"} 0.0
redis_operation_errors_total{application="icefire-game",dataType="跨服排名",operation="zrevrangewithscores",result="error"} 0.0
# HELP redis_operation_slow_total Redis slow operation count
# TYPE redis_operation_slow_total counter
redis_operation_slow_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zadd"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrangewithscores"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="全服排行榜(8个服)",operation="zrevrank"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="单服普通",operation="zadd"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="单服普通",operation="zrem"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="单服普通",operation="zrevrangewithscores"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="游戏服Instance数据",operation="hset"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="玩家邀请码",operation="get"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="竞技场分组",operation="get"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="竞技场分组",operation="hget"} 1.0
redis_operation_slow_total{application="icefire-game",dataType="竞技场分组",operation="zrevrangewithscores"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="账号服普通",operation="get"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="账号服普通",operation="hgetall"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="账号服普通",operation="set"} 0.0
redis_operation_slow_total{application="icefire-game",dataType="跨服排名",operation="zrevrangewithscores"} 0.0
# HELP system_cpu_count The number of processors available to the Java virtual machine
# TYPE system_cpu_count gauge
system_cpu_count{application="icefire-game"} 20.0
# HELP system_cpu_usage The "recent cpu usage" of the system the application is running in
# TYPE system_cpu_usage gauge
system_cpu_usage{application="icefire-game"} 0.21980269687214005
# HELP world_tick_seconds 游戏 Ticker 执行时间
# TYPE world_tick_seconds summary
world_tick_seconds_count{application="icefire-game",name="ActivityMissionTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="ActivityMissionTicker"} 1.386E-4
world_tick_seconds_count{application="icefire-game",name="ActivityScheduleTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="ActivityScheduleTicker"} 0.0184712
world_tick_seconds_count{application="icefire-game",name="ActivityWaitTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="ActivityWaitTicker"} 0.0011991
world_tick_seconds_count{application="icefire-game",name="AllianceAffairTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="AllianceAffairTicker"} 0.0091402
world_tick_seconds_count{application="icefire-game",name="AllianceBuildingTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="AllianceBuildingTicker"} 0.0011624
world_tick_seconds_count{application="icefire-game",name="AllianceFeastHallTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="AllianceFeastHallTicker"} 6.729E-4
world_tick_seconds_count{application="icefire-game",name="AllianceLeaderFightPowerUpdateTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="AllianceLeaderFightPowerUpdateTicker"} 1.366E-4
world_tick_seconds_count{application="icefire-game",name="AllianceRequestTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="AllianceRequestTicker"} 9.53E-5
world_tick_seconds_count{application="icefire-game",name="ArenaTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="ArenaTicker"} 0.0011601
world_tick_seconds_count{application="icefire-game",name="ArmyTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="ArmyTicker"} 6.525E-4
world_tick_seconds_count{application="icefire-game",name="BuffTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="BuffTicker"} 0.0030924
world_tick_seconds_count{application="icefire-game",name="CaravanTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="CaravanTicker"} 3.768E-4
world_tick_seconds_count{application="icefire-game",name="DynamicSecondTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="DynamicSecondTicker"} 6.866E-4
world_tick_seconds_count{application="icefire-game",name="EnlistmentTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="EnlistmentTicker"} 0.0013533
world_tick_seconds_count{application="icefire-game",name="GVGAlliancePointTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="GVGAlliancePointTicker"} 5.118E-4
world_tick_seconds_count{application="icefire-game",name="GVGBattleFieldTimeLineTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="GVGBattleFieldTimeLineTicker"} 4.624E-4
world_tick_seconds_count{application="icefire-game",name="GVGPiliCheAttackTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="GVGPiliCheAttackTicker"} 6.004E-4
world_tick_seconds_count{application="icefire-game",name="GVGRoleInfoTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="GVGRoleInfoTicker"} 1.541E-4
world_tick_seconds_count{application="icefire-game",name="GVGStrongHoldNodeTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="GVGStrongHoldNodeTicker"} 4.655E-4
world_tick_seconds_count{application="icefire-game",name="GVGWuChaoTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="GVGWuChaoTicker"} 0.002399
world_tick_seconds_count{application="icefire-game",name="GvgBattleServerDispatchRecordTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="GvgBattleServerDispatchRecordTicker"} 2.694E-4
world_tick_seconds_count{application="icefire-game",name="KingdomBuffTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="KingdomBuffTicker"} 5.022E-4
world_tick_seconds_count{application="icefire-game",name="KvkSeasonTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="KvkSeasonTicker"} 0.009509
world_tick_seconds_count{application="icefire-game",name="LegionMemberTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="LegionMemberTicker"} 3.126E-4
world_tick_seconds_count{application="icefire-game",name="MissionBossNodeTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="MissionBossNodeTicker"} 5.837E-4
world_tick_seconds_count{application="icefire-game",name="PeopleTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="PeopleTicker"} 0.0013295
world_tick_seconds_count{application="icefire-game",name="PlayerTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="PlayerTicker"} 0.0029558
world_tick_seconds_count{application="icefire-game",name="ResourceOutputTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="ResourceOutputTicker"} 0.0080039
world_tick_seconds_count{application="icefire-game",name="RoleCityTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="RoleCityTicker"} 0.0014875
world_tick_seconds_count{application="icefire-game",name="RoleTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="RoleTicker"} 0.0216898
world_tick_seconds_count{application="icefire-game",name="RuinsNodeTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="RuinsNodeTicker"} 6.409E-4
world_tick_seconds_count{application="icefire-game",name="SevenCaptureNodeTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="SevenCaptureNodeTicker"} 5.801E-4
world_tick_seconds_count{application="icefire-game",name="SiegeEnginesTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="SiegeEnginesTicker"} 0.0063092
world_tick_seconds_count{application="icefire-game",name="TickEntityTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="TickEntityTicker"} 6.61E-4
world_tick_seconds_count{application="icefire-game",name="TrusteeshipTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="TrusteeshipTicker"} 1.079E-4
world_tick_seconds_count{application="icefire-game",name="TvtBattleServerDispatchRecordTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="TvtBattleServerDispatchRecordTicker"} 2.665E-4
world_tick_seconds_count{application="icefire-game",name="WhispererBossTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="WhispererBossTicker"} 5.441E-4
world_tick_seconds_count{application="icefire-game",name="WorldBossDongzhuoTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="WorldBossDongzhuoTicker"} 0.0027086
world_tick_seconds_count{application="icefire-game",name="WorldBossNodeTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="WorldBossNodeTicker"} 0.0051404
world_tick_seconds_count{application="icefire-game",name="WorldExploreEventTicker"} 299
world_tick_seconds_sum{application="icefire-game",name="WorldExploreEventTicker"} 0.0030951
# HELP world_tick_seconds_max 游戏 Ticker 执行时间
# TYPE world_tick_seconds_max gauge
world_tick_seconds_max{application="icefire-game",name="ActivityMissionTicker"} 5.3E-6
world_tick_seconds_max{application="icefire-game",name="ActivityScheduleTicker"} 1.59E-4
world_tick_seconds_max{application="icefire-game",name="ActivityWaitTicker"} 2.22E-5
world_tick_seconds_max{application="icefire-game",name="AllianceAffairTicker"} 2.838E-4
world_tick_seconds_max{application="icefire-game",name="AllianceBuildingTicker"} 5.78E-5
world_tick_seconds_max{application="icefire-game",name="AllianceFeastHallTicker"} 3.49E-5
world_tick_seconds_max{application="icefire-game",name="AllianceLeaderFightPowerUpdateTicker"} 1.0E-6
world_tick_seconds_max{application="icefire-game",name="AllianceRequestTicker"} 9.0E-7
world_tick_seconds_max{application="icefire-game",name="ArenaTicker"} 2.54E-5
world_tick_seconds_max{application="icefire-game",name="ArmyTicker"} 1.12E-5
world_tick_seconds_max{application="icefire-game",name="BuffTicker"} 5.03E-5
world_tick_seconds_max{application="icefire-game",name="CaravanTicker"} 5.33E-5
world_tick_seconds_max{application="icefire-game",name="DynamicSecondTicker"} 2.41E-5
world_tick_seconds_max{application="icefire-game",name="EnlistmentTicker"} 7.83E-5
world_tick_seconds_max{application="icefire-game",name="GVGAlliancePointTicker"} 1.09E-5
world_tick_seconds_max{application="icefire-game",name="GVGBattleFieldTimeLineTicker"} 6.2E-6
world_tick_seconds_max{application="icefire-game",name="GVGPiliCheAttackTicker"} 6.95E-5
world_tick_seconds_max{application="icefire-game",name="GVGRoleInfoTicker"} 2.2E-6
world_tick_seconds_max{application="icefire-game",name="GVGStrongHoldNodeTicker"} 5.6E-6
world_tick_seconds_max{application="icefire-game",name="GVGWuChaoTicker"} 0.0010502
world_tick_seconds_max{application="icefire-game",name="GvgBattleServerDispatchRecordTicker"} 5.2E-6
world_tick_seconds_max{application="icefire-game",name="KingdomBuffTicker"} 1.5E-5
world_tick_seconds_max{application="icefire-game",name="KvkSeasonTicker"} 7.608E-4
world_tick_seconds_max{application="icefire-game",name="LegionMemberTicker"} 6.8E-6
world_tick_seconds_max{application="icefire-game",name="MissionBossNodeTicker"} 1.32E-5
world_tick_seconds_max{application="icefire-game",name="PeopleTicker"} 1.24E-5
world_tick_seconds_max{application="icefire-game",name="PlayerTicker"} 7.71E-5
world_tick_seconds_max{application="icefire-game",name="ResourceOutputTicker"} 6.096E-4
world_tick_seconds_max{application="icefire-game",name="RoleCityTicker"} 1.62E-5
world_tick_seconds_max{application="icefire-game",name="RoleTicker"} 4.835E-4
world_tick_seconds_max{application="icefire-game",name="RuinsNodeTicker"} 8.1E-6
world_tick_seconds_max{application="icefire-game",name="SevenCaptureNodeTicker"} 1.11E-5
world_tick_seconds_max{application="icefire-game",name="SiegeEnginesTicker"} 9.86E-5
world_tick_seconds_max{application="icefire-game",name="TickEntityTicker"} 1.5E-5
world_tick_seconds_max{application="icefire-game",name="TrusteeshipTicker"} 2.8E-6
world_tick_seconds_max{application="icefire-game",name="TvtBattleServerDispatchRecordTicker"} 8.93E-5
world_tick_seconds_max{application="icefire-game",name="WhispererBossTicker"} 3.19E-5
world_tick_seconds_max{application="icefire-game",name="WorldBossDongzhuoTicker"} 7.25E-5
world_tick_seconds_max{application="icefire-game",name="WorldBossNodeTicker"} 5.797E-4
world_tick_seconds_max{application="icefire-game",name="WorldExploreEventTicker"} 4.32E-5
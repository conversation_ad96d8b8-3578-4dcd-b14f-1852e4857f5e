package com.lc.billion.icefire.game.msg.handler.impl.worldBossDongzhuo;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.worldBossDongzhuo.WorldBossDongZhuoServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgDongzhuoBoxInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class CgDongzhuoBoxInfoHandler extends CgAbstractMessageHandler<CgDongzhuoBoxInfo> {
    @Autowired
    private WorldBossDongZhuoServiceImpl worldBossDongZhuoService;

    @Override
    protected void handle(Role role, CgDongzhuoBoxInfo message) {
        worldBossDongZhuoService.sendDongzhuoBoxInfo(role, message.getBoxId());
    }
}
